# -*- coding: utf-8 -*-
"""
跨进程步骤状态管理器
管理工作流步骤的执行状态和依赖关系
"""

import json
import time
import re
import os
from pathlib import Path
from typing import Optional
from services.insurance_workflow.utils.logger import get_workflow_logger


class CrossProcessStepManager:
    """跨进程步骤状态管理器"""
    
    def __init__(self, workflow_name: str = "default"):
        """
        初始化跨进程步骤管理器
        :param workflow_name: 工作流名称
        """
        self.workflow_name = workflow_name
        self.logger = get_workflow_logger()
        
        # 使用项目根目录的绝对路径下的临时目录存储状态文件，确保所有进程使用同一路径
        project_root = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        self.temp_dir = Path(project_root) / "temp_contexts"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.status_file = self.temp_dir / "step_status.json"
        self.context_file = self.temp_dir / "step_contexts.json"
        self._lock_timeout = 10  # 锁超时时间
    
    def _safe_read_json(self, file_path: Path) -> dict:
        """安全读取JSON文件"""
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            pass
        return {}
    
    def _safe_write_json(self, file_path: Path, data: dict) -> bool:
        """安全写入JSON文件"""
        try:
            # 写入临时文件，然后原子性移动
            temp_file = file_path.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 原子性移动文件
            if os.name == 'nt':  # Windows
                try:
                    if file_path.exists():
                        file_path.unlink()
                    temp_file.rename(file_path)
                except:
                    temp_file.replace(file_path)
            else:  # Unix-like
                temp_file.rename(file_path)
            
            return True
        except Exception as e:
            self.logger.error(f"写入文件失败: {e}")
            return False
    
    def mark_step_completed(self, case_index: int, step_index: int, context_id: str) -> bool:
        """
        标记步骤完成
        :param case_index: 用例索引
        :param step_index: 步骤索引
        :param context_id: 上下文ID
        :return: 是否标记成功
        """
        key = f"case:{case_index},step:{step_index}"
        
        # 确保目录存在
        self.logger.debug(f"检查目录: {self.temp_dir}, 存在: {self.temp_dir.exists()}")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.logger.debug(f"目录创建后: {self.temp_dir}, 存在: {self.temp_dir.exists()}")
        
        # 更新状态文件
        max_attempts = 10
        for attempt in range(max_attempts):
            try:
                status_data = self._safe_read_json(self.status_file)
                self.logger.debug(f"读取状态文件 {self.status_file}: {status_data}")
                status_data[key] = True
                
                if not self._safe_write_json(self.status_file, status_data):
                    raise Exception("写入状态文件失败")
                
                self.logger.debug(f"写入状态文件后: {status_data}")
                
                # 更新上下文文件
                context_data = self._safe_read_json(self.context_file)
                self.logger.debug(f"读取上下文文件 {self.context_file}: {context_data}")
                context_data[key] = context_id
                
                if not self._safe_write_json(self.context_file, context_data):
                    raise Exception("写入上下文文件失败")
                
                self.logger.debug(f"写入上下文文件后: {context_data}")
                
                # 验证文件是否真的存在和内容是否正确
                if self.status_file.exists():
                    verify_status = self._safe_read_json(self.status_file)
                    self.logger.debug(f"验证状态文件存在并读取: {verify_status}")
                else:
                    self.logger.error(f"状态文件不存在: {self.status_file}")
                
                if self.context_file.exists():
                    verify_context = self._safe_read_json(self.context_file)
                    self.logger.debug(f"验证上下文文件存在并读取: {verify_context}")
                else:
                    self.logger.error(f"上下文文件不存在: {self.context_file}")
                
                self.logger.info(f"标记步骤完成: {key}, 上下文ID: {context_id}")
                return True
                
            except Exception as e:
                self.logger.error(f"标记步骤完成异常 (尝试{attempt+1}/{max_attempts}): {e}")
                if attempt < max_attempts - 1:
                    time.sleep(0.1 * (attempt + 1))  # 指数退避
                    continue
                else:
                    self.logger.error(f"标记步骤完成失败: {e}")
                    return False
        
        return False
    
    def wait_for_step_completion(self, forwardsign: str, max_wait_time: int = 300) -> Optional[str]:
        """
        等待步骤完成
        :param forwardsign: 前置依赖标识，格式: case:X,step:Y
        :param max_wait_time: 最大等待时间（秒）
        :return: 依赖步骤的上下文ID
        """
        try:
            # 解析Forwardsign格式: case:0,step:2
            pattern = r'case:(\d+),step:(\d+)'
            match = re.match(pattern, forwardsign)
            if not match:
                self.logger.warning(f"Forwardsign格式无效: {forwardsign}, 应为 case:X,step:Y 格式")
                return None
            
            target_case_index = int(match.group(1))
            target_step_index = int(match.group(2))
            wait_key = f"case:{target_case_index},step:{target_step_index}"
            
            self.logger.info(f"等待步骤完成: {wait_key}")
            self.logger.debug(f"状态文件路径: {self.status_file.absolute()}")
            self.logger.debug(f"上下文文件路径: {self.context_file.absolute()}")
            self.logger.debug(f"状态文件存在: {self.status_file.exists()}")
            self.logger.debug(f"上下文文件存在: {self.context_file.exists()}")
            
            # 轮询等待目标步骤完成
            wait_start_time = time.time()
            check_count = 0
            while time.time() - wait_start_time < max_wait_time:
                try:
                    check_count += 1
                    status_data = self._safe_read_json(self.status_file)
                    
                    # 每10次检查打印一次调试信息
                    if check_count % 10 == 0:
                        self.logger.debug(f"等待检查 #{check_count}: 状态文件内容: {status_data}")
                        # 检查文件是否存在和大小
                        if self.status_file.exists():
                            self.logger.debug(f"状态文件存在，大小: {self.status_file.stat().st_size} bytes")
                        else:
                            self.logger.debug("状态文件不存在")
                    
                    if status_data.get(wait_key, False):
                        # 获取对应的上下文ID
                        context_data = self._safe_read_json(self.context_file)
                        target_context_id = context_data.get(wait_key)
                        self.logger.info(f"依赖步骤已完成: {wait_key}, 获取到上下文ID: {target_context_id}")
                        return target_context_id
                        
                except Exception as e:
                    self.logger.warning(f"读取步骤状态失败: {e}")
                
                time.sleep(1)  # 每秒检查一次
            
            self.logger.error(f"等待步骤完成超时: {wait_key} (等待时间: {max_wait_time}秒)")
            return None
            
        except Exception as e:
            self.logger.error(f"等待步骤完成时发生错误: {e}")
            return None
    
    def cleanup(self) -> bool:
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.logger.info(f"清理跨进程状态文件: {self.temp_dir}")
            return True
        except Exception as e:
            self.logger.warning(f"清理跨进程状态文件失败: {e}")
            return False
    
    def get_step_status(self, case_index: int, step_index: int) -> bool:
        """
        获取步骤状态
        :param case_index: 用例索引
        :param step_index: 步骤索引
        :return: 步骤是否已完成
        """
        key = f"case:{case_index},step:{step_index}"
        status_data = self._safe_read_json(self.status_file)
        return status_data.get(key, False)
    
    def get_step_context_id(self, case_index: int, step_index: int) -> Optional[str]:
        """
        获取步骤的上下文ID
        :param case_index: 用例索引
        :param step_index: 步骤索引
        :return: 上下文ID
        """
        key = f"case:{case_index},step:{step_index}"
        context_data = self._safe_read_json(self.context_file)
        return context_data.get(key)
    
    def list_completed_steps(self) -> dict:
        """
        列出所有已完成的步骤
        :return: 已完成步骤的状态字典
        """
        return self._safe_read_json(self.status_file)
