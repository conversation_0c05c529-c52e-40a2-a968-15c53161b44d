# -*- coding: utf-8 -*-
import time

import pytest

from common.readyaml import ReadYamlData
from base.removefile import remove_file
from common.dingRobot import send_dd_msg
from conf.setting import dd_msg
from conf.env_manager import env_manager

import warnings

yfd = ReadYamlData()


@pytest.fixture(scope="session", autouse=True)
def clear_extract():
    """
    pytest的fixture钩子函数，作用域为session，自动执行，每次运行用例之前执行
    主要是清除yaml文件中提取的变量数据，避免下次测试时数据污染
    :return:
    1. 清除yaml文件中提取的变量数据
    2. 删除临时文件夹中的json、txt、attach、properties等文件
    3. 删除临时文件夹
    4. 禁用HTTPS告警，ResourceWarning
    """
    # 禁用HTTPS告警，ResourceWarning
    warnings.simplefilter('ignore', ResourceWarning)

    yfd.clear_yaml_data()
    remove_file("./report/temp", ['json', 'txt', 'attach', 'properties'])


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境变量，自动应用于所有测试用例"""
    env_config = env_manager.get_current_env_config()
    
    # 将环境配置作为pytest fixture返回，可在测试用例中使用
    return env_config


def generate_test_summary(terminalreporter):
    """生成测试结果摘要字符串"""
    total = terminalreporter._numcollected
    passed = len(terminalreporter.stats.get('passed', []))
    failed = len(terminalreporter.stats.get('failed', []))
    error = len(terminalreporter.stats.get('error', []))
    skipped = len(terminalreporter.stats.get('skipped', []))
    # duration = time.time() - terminalreporter._sessionstarttime

    summary = f"""
    自动化测试结果，通知如下，请着重关注测试失败的接口，具体执行结果如下：
    测试用例总数：{total}
    测试通过数：{passed}
    测试失败数：{failed}
    错误数量：{error}
    跳过执行数量：{skipped}
    """
    print(summary)
    return summary


def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """自动收集pytest框架执行的测试结果并打印摘要信息"""
    summary = generate_test_summary(terminalreporter)
    if dd_msg:
        send_dd_msg(summary)


def pytest_configure(config):
    """配置pytest，添加环境信息"""
    env_name = env_manager.current_env
    env_config = env_manager.get_current_env_config()
    
    # 创建metadata字典（如果不存在）
    if not hasattr(config, '_metadata'):
        config._metadata = {}
    
    # 添加环境信息到pytest配置中
    config._metadata['Environment'] = env_name
    config._metadata['Base URL'] = env_config.get('base_url', '')
    config._metadata['Partner ID'] = env_config.get('partner_id', '')


def pytest_sessionfinish(session, exitstatus):
    """会话结束时清理跨进程状态文件"""
    try:
        import shutil
        from pathlib import Path
        import os
        
        # 在pytest-xdist并行执行时，只有主worker（master）才进行清理
        # 通过检查是否有workerinput属性来判断是否为worker进程
        if hasattr(session.config, 'workerinput'):
            # 这是一个worker进程，不进行清理
            print(f"Worker进程 {session.config.workerinput['workerid']} 结束，跳过清理")
            return
        
        # 清理项目根目录下的temp_contexts临时目录
        project_root = Path(os.getcwd())
        temp_contexts_dir = project_root / "temp_contexts"
        if temp_contexts_dir.exists():
            shutil.rmtree(temp_contexts_dir)  # 删除目录及其内容
            print(f"清理跨进程状态目录: {temp_contexts_dir}")
            
        # 同时清理系统临时目录下的pytest_workflow（兼容旧版本）
        import tempfile
        temp_dir = Path(tempfile.gettempdir()) / "pytest_workflow"
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"清理系统临时目录: {temp_dir}")
    except Exception as e:
        print(f"清理跨进程状态目录失败: {e}")
