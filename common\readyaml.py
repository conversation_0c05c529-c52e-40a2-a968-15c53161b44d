import yaml
import traceback
import os
from typing import List, Dict, Any

from common.recordlog import logs
from conf.operationConfig import OperationConfig
from conf.setting import FILE_PATH
from yaml.scanner import ScannerError
from .variable_processor import VariableProcessor
from conf.env_manager import env_manager
def get_testcase_yaml(yaml_file: str) -> List[tuple]:
    try:
        yaml_reader = ReadYamlData()
        yaml_data = yaml_reader.read_yaml_data(yaml_file)
        
        processed_cases = []
        for case in yaml_data:
            base_info = case.get('baseInfo', {})
            api_name = base_info.get('api_name', '')
            
            
            # 获取默认的extract配置
            default_extract = base_info.get('extract_default', {})
            
            test_cases = case.get('testCase', [])
            for test_case in test_cases:
                default_select = test_case.get('default_select', '')
                # 获取默认数据 可以根据default_select获取不同的默认数据
                try:
                    Data_Default_Path = base_info.get('data_default', '')
                    # 自动加载默认数据yaml
                    default_data = {}
                    if Data_Default_Path:
                        default_yaml_path = f'./testcase/default_yaml/{Data_Default_Path}'
                        if os.path.exists(default_yaml_path):
                            with open(default_yaml_path, 'r', encoding='utf-8') as f:
                                default_yaml = yaml.safe_load(f)
                                if default_select:
                                    default_data = default_yaml.get(default_select, {})
                                else:
                                    default_data = default_yaml.get('data_default', {})
                except Exception as e:
                    logs.error(f"读取默认数据失败: {str(e)}")
                case_json = test_case.get('data', {})
                
                base_json = VariableProcessor.merge_json_data(default_data, case_json)
                
                processed_json = VariableProcessor.process_json_data(base_json)
                test_case['data'] = processed_json
                
                # 处理extract配置
                if 'extract' in test_case:
                    # 如果extract显式设置为None，则删除extract配置
                    if test_case['extract'] is None:
                        test_case.pop('extract', None)
                elif default_extract:
                    # 如果没有extract配置，使用默认配置
                    test_case['extract'] = default_extract
                
                testcase = test_case.copy()
                processed_cases.append((base_info, testcase))
                
        return processed_cases
    except Exception as e:
        logs.error(f"读取测试用例数据失败: {str(e)}")
        raise e


class ReadYamlData:
    """读写接口的YAML格式测试数据"""
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ReadYamlData, cls).__new__(cls)
        return cls._instance
    def __init__(self):
        if not ReadYamlData._initialized:
            self.yaml_data = None
            self.conf = OperationConfig()
            self.env_variables = {}
            self._load_env_variables()
            ReadYamlData._initialized = True


    def _load_env_variables(self):
        """加载环境变量配置"""
        try:
            env_name = env_manager.current_env
            variables_path = os.path.join('conf', 'variables.yaml')
            
            if os.path.exists(variables_path):
                with open(variables_path, 'r', encoding='utf-8') as f:
                    all_variables = yaml.safe_load(f) or {}
                    # 获取当前环境的变量
                    self.env_variables = all_variables.get(env_name, {})
                    logs.info(f"已加载 {env_name} 环境变量配置")
            else:
                logs.warning(f"环境变量配置文件不存在: {variables_path}")
        except Exception as e:
            logs.error(f"加载环境变量配置失败: {str(e)}")
    def reload_env_variables(self):
        """重新加载环境变量配置"""
        self._load_env_variables()
    def clear_env_variables(self):
        """清理当前环境的变量"""
        current_env = env_manager.current_env
        self.env_variables[current_env] = {}
        
    def save_env_variable(self, key: str, value: Any):
        """保存环境变量"""
        current_env = env_manager.current_env
        if current_env not in self.env_variables:
            self.env_variables[current_env] = {}
        self.env_variables[current_env][key] = value
    def get_env_variable(self, key: str, default: Any = None) -> Any:
        """
        获取环境变量值
        :param key: 变量名
        :param default: 默认值
        :return: 变量值
        """
        return self.env_variables.get(key, default)

    def read_yaml_data(self, yaml_file: str) -> List[Dict[str, Any]]:
        """
        读取YAML文件数据
        :param yaml_file: YAML文件路径
        :return: YAML数据列表
        """
        if not os.path.exists(yaml_file):
            raise FileNotFoundError(f"YAML文件不存在: {yaml_file}")
            
        with open(yaml_file, 'r', encoding='utf-8') as f:
            self.yaml_data = yaml.safe_load(f)
        return self.yaml_data

    def write_yaml_data(self, value):
        """
        写入数据需为dict，allow_unicode=True表示写入中文，sort_keys按顺序写入
        写入YAML文件数据,主要用于接口关联
        :param value: 写入数据，必须用dict
        :return:
        """
        file = None
        file_path = FILE_PATH['EXTRACT']
        if not os.path.exists(file_path):
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            open(file_path, 'w').close()
            
        try:
            # 读取现有数据
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = yaml.safe_load(f) or {}
            
            # 获取当前环境
            from conf.env_manager import env_manager
            current_env = env_manager.current_env
            
            # 确保环境键存在
            if current_env not in existing_data:
                existing_data[current_env] = {}
            
            # 合并新数据到当前环境
            if isinstance(value, dict):
                existing_data[current_env].update(value)
                
                # 写入更新后的数据
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(existing_data, f, allow_unicode=True, sort_keys=False)
                logs.info(f'已更新 {current_env} 环境的变量数据')
            else:
                logs.info('写入[extract.yaml]的数据必须为dict格式')
        except Exception as e:
            logs.error(f"写入变量数据失败: {str(e)}")
            logs.error(str(traceback.format_exc()))
        finally:
            if file:
                file.close()

    def clear_yaml_data(self):
        """清除YAML数据"""
        self.yaml_data = None

    def get_extract_yaml(self, node_name, second_node_name=None):
        """
        用于读取接口提取的变量值
        :param node_name: 节点名称
        :param second_node_name: 二级节点名称
        :return: 提取的值
        """
        if not os.path.exists(FILE_PATH['EXTRACT']):
            logs.error('extract.yaml不存在')
            os.makedirs(os.path.dirname(FILE_PATH['EXTRACT']), exist_ok=True)
            open(FILE_PATH['EXTRACT'], 'w').close()
            logs.info('extract.yaml创建成功！')
            
        try:
            with open(FILE_PATH['EXTRACT'], 'r', encoding='utf-8') as rf:
                ext_data = yaml.safe_load(rf) or {}
                
                # 获取当前环境
                from conf.env_manager import env_manager
                current_env = env_manager.current_env
                
                # 获取当前环境的数据
                env_data = ext_data.get(current_env, {})
                
                if second_node_name is None:
                    return env_data.get(node_name)
                else:
                    return env_data.get(node_name, {}).get(second_node_name)
        except Exception as e:
            logs.error(f"【extract.yaml】没有找到：{node_name},--%s" % e)
            return None

    def get_method(self):
        """
        获取请求方法
        :return: 请求方法
        """
        if not self.yaml_data:
            return None
        yaml_data = self.read_yaml_data(self.yaml_data)
        return yaml_data[0].get('method') if yaml_data else None

    def get_request_parame(self):
        """
        获取yaml测试数据中的请求参数
        :return: 请求参数列表
        """
        if not self.yaml_data:
            return []
            
        yaml_data = self.read_yaml_data(self.yaml_data)
        if not yaml_data:
            return []
            
        return yaml_data[1:] if len(yaml_data) > 1 else []

    def get_workflow_yaml(self, file_path):
        """
        获取工作流测试用例数据，增强错误处理
        :param file_path: yaml文件路径
        :return: 返回工作流测试用例列表，格式：[(workflow_info, base_info, testcase), ...]
        """
        try:
            import os
            
            logs.info(f"开始读取工作流文件: {file_path}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                error_msg = f"工作流文件不存在: {file_path}"
                logs.error(error_msg)
                return []
            
            # 读取工作流文件
            workflow_data = self.read_yaml_data(file_path)
            workflow_cases = workflow_data.get('workflow_testcases', [])
            
            if not workflow_cases:
                logs.warning(f"工作流文件中没有找到测试用例: {file_path}")
                return []
            
            processed_workflows = []
            
            for workflow in workflow_cases:
                workflow_name = workflow.get('workflow_name', '')
                description = workflow.get('description', '')
                steps = workflow.get('steps', [])
                
                logs.info(f"处理工作流: {workflow_name}, 步骤数: {len(steps)}")
                
                # 收集所有步骤的数据列表长度，用于验证一致性
                data_list_lengths = []
                step_data_info = []
                
                # 第一遍遍历：收集数据信息和验证
                for step_index, step in enumerate(steps):
                    step_name = step.get('step_name', f'步骤{step_index + 1}')
                    step_data = step.get('data', {})
                    data_default_path = step.get('data_default', '')
                    
                    # 获取默认数据
                    default_data = {}
                    if data_default_path:
                        try:
                            default_yaml_path = data_default_path
                            if not default_yaml_path.endswith(('.yaml', '.yml')):
                                # 如果路径没有扩展名，尝试添加
                                for ext in ['.yaml', '.yml']:
                                    test_path = default_yaml_path + ext
                                    if os.path.exists(test_path):
                                        default_yaml_path = test_path
                                        break
                            
                            if os.path.exists(default_yaml_path):
                                with open(default_yaml_path, 'r', encoding='utf-8') as f:
                                    default_yaml = yaml.safe_load(f)
                                    default_data = default_yaml.get('data_default', {})
                                    logs.info(f"成功加载步骤 {step_name} 的默认数据")
                            else:
                                logs.warning(f"步骤 {step_name} 的默认数据文件不存在: {default_yaml_path}")
                        except Exception as e:
                            logs.error(f"读取步骤 {step_name} 默认数据失败: {str(e)}")
                    
                    # 判断数据类型并记录
                    if isinstance(step_data, list):
                        # 过滤掉空的数据项
                        # filtered_data = [item for item in step_data if item is not None and item != {}]
                        data_list_lengths.append(len(step_data))
                        step_data_info.append({
                            'step_name': step_name,
                            'is_list': True,
                            'data_count': len(step_data),
                            'data': step_data,
                            'default_data': default_data,
                            'step_config': step
                        })
                        logs.info(f"步骤 {step_name} 包含 {len(step_data)} 个数据项")
                    else:
                        step_data_info.append({
                            'step_name': step_name,
                            'is_list': False,
                            'data_count': 1,
                            'data': step_data,
                            'default_data': default_data,
                            'step_config': step
                        })
                        logs.info(f"步骤 {step_name} 包含通用数据")
                
                # 验证数据列表长度一致性
                unique_lengths = list(set(data_list_lengths))
                if len(unique_lengths) > 1:
                    error_msg = f"工作流 {workflow_name} 中的数据列表长度不一致: {data_list_lengths}"
                    logs.error(error_msg)
                    raise ValueError(error_msg)
                
                # 确定测试用例数量
                test_case_count = unique_lengths[0] if unique_lengths else 1
                logs.info(f"工作流 {workflow_name} 将生成 {test_case_count} 个测试用例")
                case_info = []                # 第二遍遍历：生成测试用例
                for case_index in range(test_case_count):
                    workflow_info = {
                        'workflow_name': f"{workflow_name}_用例{case_index + 1}" if test_case_count > 1 else workflow_name,
                        'description': description,
                        'original_workflow_name': workflow_name,
                        'case_index': case_index,
                        'total_cases': test_case_count
                    }
                    
                    case_info = []  # 当前用例的所有步骤
                    previous_step_skipped = False  # 跟踪上一步是否被跳过
                    
                    # 为每个步骤生成对应的测试用例
                    for step_index, step_info in enumerate(step_data_info):
                        step_config = step_info['step_config']
                        step_name = step_info['step_name']
                        default_data = step_info['default_data']
                        
                        # 构建base_info
                        base_info = step_config.get('api_config', {}).copy()
                        if 'data_default' in step_config:
                            base_info['data_default'] = step_config['data_default']
                          # 判断是否为通用接口
                        # 通用接口的判断标准：
                        # 1. 数据不是列表（即字典格式），或者
                        # 2. 是列表但只有一个数据项或数据为空
                        is_generic_step = not step_info['is_list'] or step_info['data_count'] <= 1
                        
                        # 获取当前用例的数据
                        current_data = {}
                        skip_execution = False
                        forwardsign = None
                        
                        if step_info['is_list'] and step_info['data_count'] > 1:
                            # 有多个数据的步骤（非通用接口）
                            if case_index < len(step_info['data']):
                                current_data = step_info['data'][case_index]
                                skip_execution = current_data.get('skip_execution', False)
                                forwardsign = current_data.pop('Forwardsign', None)  # 获取依赖标识并从数据中移除
                            else:
                                logs.warning(f"步骤 {step_name} 的数据索引 {case_index} 超出范围")
                                current_data = {}
                        else:
                            # 通用接口或只有一个数据的步骤
                            if step_info['is_list'] and len(step_info['data']) > 0:
                                current_data = step_info['data'][0]  # 使用第一个数据
                            else:
                                current_data = step_info['data']  # 字典格式的数据
                            
                            # 通用接口根据上一步跳过情况决定是否跳过
                            if is_generic_step and previous_step_skipped:
                                skip_execution = True
                                logs.info(f"通用接口步骤 {step_name}_用例{case_index + 1} 因上一步跳过而自动跳过")
                            else:
                                # 从当前数据中获取skip_execution（如果有的话）
                                if isinstance(current_data, dict):
                                    skip_execution = current_data.get('skip_execution', False)
                                else:
                                    skip_execution = False
                            
                            # 获取依赖标识
                            if isinstance(current_data, dict):
                                forwardsign = current_data.pop('Forwardsign', None)  # 获取依赖标识并从数据中移除
                            else:
                                forwardsign = None
                        
                        # 合并默认数据和当前数据
                        merged_data = VariableProcessor.merge_json_data(default_data, current_data)                        
                        # 处理跳过执行标识
                        # skip_execution 和 forwardsign 已在上面处理
                        
                        # 构建testcase
                        testcase = {
                            'case_index': case_index,
                            'case_name': f"{step_name}_用例{case_index + 1}" if test_case_count > 1 else step_name,
                            'data': merged_data,
                            'validation': step_config.get('validation', []),
                            'extract': step_config.get('extract', {}),
                            'max_retry': step_config.get('max_retry', 1),
                            'retry_interval': step_config.get('retry_interval', 1),
                            'skip_execution': skip_execution,
                            'forwardsign': forwardsign,
                            'is_generic_step': is_generic_step
                        }
                        
                        # 处理extract配置
                        if 'extract' in testcase:
                            if testcase['extract'] is None or testcase['extract'] == {}:
                                testcase.pop('extract', None)
                        
                        # 更新previous_step_skipped状态
                        previous_step_skipped = skip_execution
                        
                        # Append tuple of workflow_info, base_info, and testcase
                        case_info.append((workflow_info.copy(), base_info.copy(), testcase.copy()))
                        logs.debug(f"生成测试用例: {testcase['case_name']}, 跳过: {skip_execution}, 通用接口: {is_generic_step}")# 每个用例作为独立的测试用例组，用于pytest并行执行
                    processed_workflows.append(case_info)

            logs.info(f"工作流文件处理完成，共生成 {len(processed_workflows)} 个测试用例")
            return processed_workflows  # 返回多个列表，每个列表是一个测试用例的所有步骤
            
        except Exception as e:
            error_msg = f"读取工作流测试用例数据失败: {str(e)}"
            logs.error(error_msg)
            logs.error(f"详细错误: {traceback.format_exc()}")
            return []