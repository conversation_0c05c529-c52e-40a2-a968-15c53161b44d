# 工作流跳过和依赖功能修复总结

## 修复的问题

### 1. 并行执行支持
**问题**：之前返回单个列表，无法支持pytest-xdist并行执行
**修复**：
- 修改 `readyaml.py` 中的 `get_workflow_yaml` 方法
- 返回多个列表（每个测试用例一个列表），而不是单个合并列表
- 每个测试用例可以独立并行执行

### 2. 通用接口处理
**问题**：通用接口不需要多个data数据，应该根据上一步跳过情况自动跳过
**修复**：
- 在YAML解析阶段识别通用接口（`is_generic_step`）
- 通用接口根据前一步骤的跳过状态自动设置跳过标识
- 简化了测试执行逻辑

### 3. 步骤索引计算
**问题**：复杂的步骤索引计算导致依赖关系混乱
**修复**：
- 简化步骤索引计算，每个测试用例内部步骤索引从0开始
- 移除复杂的跨用例步骤计算逻辑

## 主要代码修改

### readyaml.py
```python
# 1. 修改返回结构，支持并行执行
processed_workflows.append(case_info)  # 每个用例独立
return processed_workflows  # 返回多个列表

# 2. 增强通用接口处理
is_generic_step = not step_info['is_list'] or step_info['data_count'] <= 1
if is_generic_step and previous_step_skipped:
    skip_execution = True
    
# 3. 增加步骤类型标识
testcase['is_generic_step'] = is_generic_step
```

### test_deliver_workflow.py
```python
# 1. 简化步骤索引计算
real_step_index = step_index  # 直接使用步骤索引

# 2. 简化跳过逻辑
if skip_execution:
    logs.info(f"跳过执行步骤: {step_name}")
    continue  # 移除复杂的通用接口跳过逻辑
```

## 功能验证

### YAML配置示例
```yaml
workflow_testcases:
  - workflow_name: "投保-理赔-多数据组测试"
    steps:
      - step_name: "投保"
        data:
          - caseName: "投保用例1"
            # 正常数据
          - caseName: "投保用例2"  
            skip_execution: true  # 跳过执行
            
      - step_name: "投保状态查询"
        data:
          - tradeSerialNo: "{{tradeSerialNo}}"
          - Forwardsign: "case:0,step:0"  # 等待用例0步骤0完成
            tradeSerialNo: "{{tradeSerialNo}}"
            
      - step_name: "申请理赔"
        data:
          - # 正常理赔数据
          - Forwardsign: "case:0,step:2"  # 等待用例0步骤2完成
            # 理赔数据
```

### 执行结果
- ✅ 生成2个独立的测试用例，支持并行执行
- ✅ 用例2步骤0跳过执行
- ✅ 用例2步骤1等待用例1步骤0完成
- ✅ 用例2步骤2等待用例1步骤2完成
- ✅ 依赖步骤的上下文ID正确传递

## 使用说明

1. **跳过执行**：在data数组中添加 `skip_execution: true`
2. **依赖等待**：在data数组中添加 `Forwardsign: "case:X,step:Y"`
3. **并行执行**：使用pytest-xdist，每个测试用例独立运行
4. **变量传递**：依赖步骤的上下文ID会自动传递给等待步骤

## 文件结构
- `common/readyaml.py`: 工作流解析逻辑
- `testcase/DeliveryService/test_deliver_workflow.py`: 工作流执行逻辑
- `testcase/DeliveryService/insureInvoce copy.yml`: 测试用例配置
- 测试脚本: `debug_workflow.py`, `test_complete_workflow.py`
