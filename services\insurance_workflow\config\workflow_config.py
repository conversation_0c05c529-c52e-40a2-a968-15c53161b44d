# -*- coding: utf-8 -*-
"""
保险理赔工作流配置管理模块
提供工作流配置的统一管理和参数处理功能
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class StepConfig:
    """单个步骤的配置信息"""
    step_name: str
    api_config: Dict[str, Any]
    data: Any = None
    data_default: str = ""
    validation: List[Dict] = field(default_factory=list)
    extract: Dict[str, str] = field(default_factory=dict)
    max_retry: int = 1
    retry_interval: int = 1
    skip_execution: bool = False
    forwardsign: Optional[str] = None
    is_generic_step: bool = False


@dataclass
class WorkflowConfig:
    """工作流配置信息"""
    workflow_name: str
    description: str = ""
    steps: List[StepConfig] = field(default_factory=list)
    case_index: int = 0
    total_cases: int = 1
    original_workflow_name: str = ""


class WorkflowConfigManager:
    """工作流配置管理器"""
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化配置管理器
        :param project_root: 项目根目录路径，如果为None则自动检测
        """
        if project_root is None:
            # 自动检测项目根目录
            current_file = Path(__file__).resolve()
            # 向上查找直到找到包含testcase目录的根目录
            for parent in current_file.parents:
                if (parent / "testcase").exists():
                    project_root = str(parent)
                    break
            else:
                # 如果找不到，使用当前工作目录
                project_root = os.getcwd()
        
        self.project_root = Path(project_root)
        self.default_yaml_dir = self.project_root / "testcase" / "default_yaml"
        
    def load_workflow_from_yaml(self, yaml_file_path: str) -> List[List[tuple]]:
        """
        从YAML文件加载工作流配置
        :param yaml_file_path: YAML文件路径（相对于项目根目录）
        :return: 工作流配置列表，格式：[[(workflow_info, base_info, testcase), ...], ...]
        """
        from services.insurance_workflow.utils.yaml_reader import WorkflowYamlReader
        
        yaml_reader = WorkflowYamlReader(str(self.project_root))
        return yaml_reader.get_workflow_yaml(yaml_file_path)
    
    def create_step_config(self, workflow_info: Dict, base_info: Dict, testcase: Dict) -> StepConfig:
        """
        创建步骤配置对象
        :param workflow_info: 工作流信息
        :param base_info: 基础API配置信息
        :param testcase: 测试用例信息
        :return: StepConfig对象
        """
        return StepConfig(
            step_name=testcase.get('case_name', '未命名步骤'),
            api_config=base_info.copy(),
            data=testcase.get('data', {}),
            data_default=base_info.get('data_default', ''),
            validation=testcase.get('validation', []),
            extract=testcase.get('extract', {}),
            max_retry=testcase.get('max_retry', 1),
            retry_interval=testcase.get('retry_interval', 1),
            skip_execution=testcase.get('skip_execution', False),
            forwardsign=testcase.get('forwardsign'),
            is_generic_step=testcase.get('is_generic_step', False)
        )
    
    def create_workflow_config(self, workflow_steps: List[tuple]) -> WorkflowConfig:
        """
        创建工作流配置对象
        :param workflow_steps: 工作流步骤列表，格式：[(workflow_info, base_info, testcase), ...]
        :return: WorkflowConfig对象
        """
        if not workflow_steps:
            raise ValueError("工作流步骤列表不能为空")
        
        # 从第一个步骤获取工作流信息
        first_workflow_info, _, _ = workflow_steps[0]
        
        workflow_config = WorkflowConfig(
            workflow_name=first_workflow_info.get('workflow_name', '未命名工作流'),
            description=first_workflow_info.get('description', ''),
            case_index=first_workflow_info.get('case_index', 0),
            total_cases=first_workflow_info.get('total_cases', 1),
            original_workflow_name=first_workflow_info.get('original_workflow_name', '')
        )
        
        # 创建步骤配置
        for workflow_info, base_info, testcase in workflow_steps:
            step_config = self.create_step_config(workflow_info, base_info, testcase)
            workflow_config.steps.append(step_config)
        
        return workflow_config
    
    def get_default_data_path(self, relative_path: str) -> str:
        """
        获取默认数据文件的完整路径
        :param relative_path: 相对路径
        :return: 完整路径
        """
        if relative_path.startswith('./'):
            # 相对于项目根目录的路径
            return str(self.project_root / relative_path[2:])
        elif relative_path.startswith('/'):
            # 绝对路径
            return relative_path
        else:
            # 相对于default_yaml目录的路径
            return str(self.default_yaml_dir / relative_path)
    
    def validate_workflow_config(self, config: WorkflowConfig) -> bool:
        """
        验证工作流配置的有效性
        :param config: 工作流配置
        :return: 是否有效
        """
        if not config.workflow_name:
            return False
        
        if not config.steps:
            return False
        
        for step in config.steps:
            if not step.step_name:
                return False
            if not step.api_config:
                return False
        
        return True


class DefaultConfigProvider:
    """默认配置提供器"""
    
    @staticmethod
    def get_default_workflow_config() -> Dict[str, Any]:
        """获取默认的工作流配置"""
        return {
            'max_retry': 1,
            'retry_interval': 1,
            'timeout': 300,
            'enable_allure': True,
            'enable_logging': True
        }
    
    @staticmethod
    def get_default_step_config() -> Dict[str, Any]:
        """获取默认的步骤配置"""
        return {
            'max_retry': 1,
            'retry_interval': 1,
            'skip_execution': False,
            'validation': [],
            'extract': {}
        }
    
    @staticmethod
    def get_insurance_claim_templates() -> Dict[str, str]:
        """获取保险理赔相关的模板路径"""
        return {
            'insure': './testcase/default_yaml/deliver/insure.yml',
            'query': './testcase/default_yaml/deliver/query.yml',
            'applyclaim': './testcase/default_yaml/deliver/applyclaim.yml'
        }
