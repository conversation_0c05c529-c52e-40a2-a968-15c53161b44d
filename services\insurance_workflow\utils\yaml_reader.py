# -*- coding: utf-8 -*-
"""
YAML文件读取器
提供工作流YAML文件的读取和解析功能
"""

import yaml
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import traceback

from services.insurance_workflow.utils.logger import get_workflow_logger
from services.insurance_workflow.utils.variable_processor import WorkflowVariableProcessor


class WorkflowYamlReader:
    """工作流YAML文件读取器"""
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化YAML读取器
        :param project_root: 项目根目录路径
        """
        if project_root is None:
            # 自动检测项目根目录
            current_file = Path(__file__).resolve()
            for parent in current_file.parents:
                if (parent / "testcase").exists():
                    project_root = str(parent)
                    break
            else:
                project_root = os.getcwd()
        
        self.project_root = Path(project_root)
        self.logger = get_workflow_logger()
        self.variable_processor = WorkflowVariableProcessor()
    
    def read_yaml_data(self, yaml_file: str) -> Dict[str, Any]:
        """
        读取YAML文件数据
        :param yaml_file: YAML文件路径
        :return: YAML数据
        """
        # 处理相对路径
        if not os.path.isabs(yaml_file):
            yaml_file = str(self.project_root / yaml_file)
        
        if not os.path.exists(yaml_file):
            raise FileNotFoundError(f"YAML文件不存在: {yaml_file}")
        
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                yaml_data = yaml.safe_load(f)
            return yaml_data or {}
        except yaml.YAMLError as e:
            self.logger.error(f"YAML文件解析错误: {e}")
            raise e
        except Exception as e:
            self.logger.error(f"读取YAML文件失败: {e}")
            raise e
    
    def get_workflow_yaml(self, file_path: str) -> List[List[tuple]]:
        """
        获取工作流测试用例数据
        :param file_path: yaml文件路径
        :return: 返回工作流测试用例列表，格式：[[(workflow_info, base_info, testcase), ...], ...]
        """
        try:
            self.logger.info(f"开始读取工作流文件: {file_path}")
            
            # 处理相对路径
            if not os.path.isabs(file_path):
                file_path = str(self.project_root / file_path)
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                error_msg = f"工作流文件不存在: {file_path}"
                self.logger.error(error_msg)
                return []
            
            # 读取工作流文件
            workflow_data = self.read_yaml_data(file_path)
            workflow_cases = workflow_data.get('workflow_testcases', [])
            
            if not workflow_cases:
                self.logger.warning(f"工作流文件中没有找到测试用例: {file_path}")
                return []
            
            processed_workflows = []
            
            for workflow in workflow_cases:
                workflow_name = workflow.get('workflow_name', '')
                description = workflow.get('description', '')
                steps = workflow.get('steps', [])
                
                self.logger.info(f"处理工作流: {workflow_name}, 步骤数: {len(steps)}")
                
                # 处理工作流步骤
                processed_cases = self._process_workflow_steps(workflow_name, description, steps)
                processed_workflows.extend(processed_cases)
            
            self.logger.info(f"工作流文件处理完成，共生成 {len(processed_workflows)} 个测试用例")
            return processed_workflows
            
        except Exception as e:
            error_msg = f"读取工作流测试用例数据失败: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _process_workflow_steps(self, workflow_name: str, description: str, 
                               steps: List[Dict]) -> List[List[tuple]]:
        """
        处理工作流步骤
        :param workflow_name: 工作流名称
        :param description: 工作流描述
        :param steps: 步骤列表
        :return: 处理后的工作流用例列表
        """
        # 收集所有步骤的数据列表长度，用于验证一致性
        data_list_lengths = []
        step_data_info = []
        
        # 第一遍遍历：收集数据信息和验证
        for step_index, step in enumerate(steps):
            step_name = step.get('step_name', f'步骤{step_index + 1}')
            step_data = step.get('data', {})
            data_default_path = step.get('data_default', '')
            
            # 获取默认数据
            default_data = self._load_default_data(data_default_path, step_name)
            
            # 判断数据类型并记录
            if isinstance(step_data, list):
                data_list_lengths.append(len(step_data))
                step_data_info.append({
                    'step_name': step_name,
                    'is_list': True,
                    'data_count': len(step_data),
                    'data': step_data,
                    'default_data': default_data,
                    'step_config': step
                })
                self.logger.info(f"步骤 {step_name} 包含 {len(step_data)} 个数据项")
            else:
                step_data_info.append({
                    'step_name': step_name,
                    'is_list': False,
                    'data_count': 1,
                    'data': step_data,
                    'default_data': default_data,
                    'step_config': step
                })
                self.logger.info(f"步骤 {step_name} 包含通用数据")
        
        # 验证数据列表长度一致性
        unique_lengths = list(set(data_list_lengths))
        if len(unique_lengths) > 1:
            error_msg = f"工作流 {workflow_name} 中的数据列表长度不一致: {data_list_lengths}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        # 确定测试用例数量
        test_case_count = unique_lengths[0] if unique_lengths else 1
        self.logger.info(f"工作流 {workflow_name} 将生成 {test_case_count} 个测试用例")
        
        # 第二遍遍历：生成测试用例
        processed_workflows = []
        for case_index in range(test_case_count):
            workflow_info = {
                'workflow_name': f"{workflow_name}_用例{case_index + 1}" if test_case_count > 1 else workflow_name,
                'description': description,
                'original_workflow_name': workflow_name,
                'case_index': case_index,
                'total_cases': test_case_count
            }
            
            case_info = []  # 当前用例的所有步骤
            previous_step_skipped = False  # 跟踪上一步是否被跳过
            
            # 为每个步骤生成对应的测试用例
            for step_index, step_info in enumerate(step_data_info):
                step_config = step_info['step_config']
                step_name = step_info['step_name']
                default_data = step_info['default_data']
                
                # 构建base_info
                base_info = step_config.get('api_config', {}).copy()
                if 'data_default' in step_config:
                    base_info['data_default'] = step_config['data_default']
                
                # 判断是否为通用接口
                is_generic_step = not step_info['is_list'] or step_info['data_count'] <= 1
                
                # 获取当前用例的数据
                current_data, skip_execution, forwardsign = self._get_step_data(
                    step_info, case_index, is_generic_step, previous_step_skipped
                )
                
                # 合并默认数据和当前数据
                merged_data = self.variable_processor.merge_json_data(default_data, current_data)
                
                # 构建testcase
                testcase = {
                    'case_index': case_index,
                    'case_name': f"{step_name}_用例{case_index + 1}" if test_case_count > 1 else step_name,
                    'data': merged_data,
                    'validation': step_config.get('validation', []),
                    'extract': step_config.get('extract', {}),
                    'max_retry': step_config.get('max_retry', 1),
                    'retry_interval': step_config.get('retry_interval', 1),
                    'skip_execution': skip_execution,
                    'forwardsign': forwardsign,
                    'is_generic_step': is_generic_step
                }
                
                # 处理extract配置
                if 'extract' in testcase:
                    if testcase['extract'] is None or testcase['extract'] == {}:
                        testcase.pop('extract', None)
                
                # 更新previous_step_skipped状态
                previous_step_skipped = skip_execution
                
                # 添加到用例信息中
                case_info.append((workflow_info.copy(), base_info.copy(), testcase.copy()))
                self.logger.debug(f"生成测试用例: {testcase['case_name']}, 跳过: {skip_execution}, 通用接口: {is_generic_step}")
            
            # 每个用例作为独立的测试用例组
            processed_workflows.append(case_info)
        
        return processed_workflows
    
    def _load_default_data(self, data_default_path: str, step_name: str) -> Dict[str, Any]:
        """加载默认数据"""
        default_data = {}
        if data_default_path:
            try:
                default_yaml_path = data_default_path
                if not default_yaml_path.endswith(('.yaml', '.yml')):
                    # 如果路径没有扩展名，尝试添加
                    for ext in ['.yaml', '.yml']:
                        test_path = default_yaml_path + ext
                        if os.path.exists(test_path):
                            default_yaml_path = test_path
                            break
                
                if os.path.exists(default_yaml_path):
                    with open(default_yaml_path, 'r', encoding='utf-8') as f:
                        default_yaml = yaml.safe_load(f)
                        default_data = default_yaml.get('data_default', {})
                        self.logger.info(f"成功加载步骤 {step_name} 的默认数据")
                else:
                    self.logger.warning(f"步骤 {step_name} 的默认数据文件不存在: {default_yaml_path}")
            except Exception as e:
                self.logger.error(f"读取步骤 {step_name} 默认数据失败: {str(e)}")
        
        return default_data
    
    def _get_step_data(self, step_info: Dict, case_index: int, is_generic_step: bool, 
                      previous_step_skipped: bool) -> tuple:
        """获取步骤数据"""
        current_data = {}
        skip_execution = False
        forwardsign = None
        
        if step_info['is_list'] and step_info['data_count'] > 1:
            # 有多个数据的步骤（非通用接口）
            if case_index < len(step_info['data']):
                current_data = step_info['data'][case_index]
                skip_execution = current_data.get('skip_execution', False)
                forwardsign = current_data.pop('Forwardsign', None)  # 获取依赖标识并从数据中移除
            else:
                self.logger.warning(f"步骤 {step_info['step_name']} 的数据索引 {case_index} 超出范围")
                current_data = {}
        else:
            # 通用接口或只有一个数据的步骤
            if step_info['is_list'] and len(step_info['data']) > 0:
                current_data = step_info['data'][0]  # 使用第一个数据
            else:
                current_data = step_info['data']  # 字典格式的数据
            
            # 通用接口根据上一步跳过情况决定是否跳过
            if is_generic_step and previous_step_skipped:
                skip_execution = True
                self.logger.info(f"通用接口步骤 {step_info['step_name']}_用例{case_index + 1} 因上一步跳过而自动跳过")
            else:
                # 从当前数据中获取skip_execution（如果有的话）
                if isinstance(current_data, dict):
                    skip_execution = current_data.get('skip_execution', False)
                else:
                    skip_execution = False
            
            # 获取依赖标识
            if isinstance(current_data, dict):
                forwardsign = current_data.pop('Forwardsign', None)  # 获取依赖标识并从数据中移除
            else:
                forwardsign = None
        
        return current_data, skip_execution, forwardsign
