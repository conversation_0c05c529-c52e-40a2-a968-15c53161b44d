import json
import allure
import pytest
import requests
import urllib3
import time

from conf import setting
from common.recordlog import logs
from requests import utils
from common.readyaml import ReadYamlData
from urllib3.exceptions import InsecureRequestWarning
from common.encryption import RequestEncryption
from conf.env_manager import env_manager

class SendRequest:
    """发送接口请求，暂时只写了get和post方法的请求"""

    def __init__(self, cookie=None):
        self.cookie = cookie
        self.read = ReadYamlData()
        self.RequestEncryption = RequestEncryption()
        self.env_config = env_manager.get_current_env_config()

    def get(self, url, data, header):
        """
        :param url: 接口地址
        :param data: 请求参数
        :param header: 请求头
        :return:
        """
        urllib3.disable_warnings(InsecureRequestWarning)  # 控制台输出InsecureRequestWarning错误
        try:
            if data is None:
                response = requests.get(url, headers=header, cookies=self.cookie, verify=False)
            else:
                response = requests.get(url, data, headers=header, cookies=self.cookie, verify=False)
        except requests.RequestException as e:
            logs.error(e)
            return None
        except Exception as e:
            logs.error(e)
            return None
        # 响应时间/毫秒
        res_ms = response.elapsed.microseconds / 1000
        # 响应时间/秒
        res_second = response.elapsed.total_seconds()
        response_dict = dict()

        # 接口响应状态码
        response_dict['code'] = response.status_code
        # 接口响应文本
        response_dict['text'] = response.text
        try:
            response_dict['body'] = response.json().get('body')
        except Exception:
            response_dict['body'] = ''
        response_dict['res_ms'] = res_ms
        response_dict['res_second'] = res_second
        return response_dict

    def post(self, url, data, header):
        """
        :param url:
        :param data: verify=False忽略SSL证书验证
        :param header:
        :return:
        """
        # 控制台输出InsecureRequestWarning错误
        requests.packages.urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        try:
            if data is None:
                response = requests.post(url, header, cookies=self.cookie, verify=False)
            else:
                response = requests.post(url, data, headers=header, cookies=self.cookie, verify=False)
        except requests.RequestException as e:
            logs.error(e)
            return None
        except Exception as e:
            logs.error(e)
            return None
        # 响应时间/毫秒
        res_ms = response.elapsed.microseconds / 1000
        # 响应时间/秒
        res_second = response.elapsed.total_seconds()
        response_dict = dict()
        # 接口响应状态码
        response_dict['code'] = response.status_code
        # 接口响应文本
        response_dict['text'] = response.text
        try:
            response_dict['body'] = response.json().get('body')
        except Exception:
            response_dict['body'] = ''
        response_dict['res_ms'] = res_ms
        response_dict['res_second'] = res_second
        return response_dict

    def send_request(self, **kwargs):
        """
        发送HTTP请求
        :param kwargs: 请求参数
        :return: 响应结果
        """
        result = None
        cookie = {}
        try:
            logs.info("发送请求参数：%s" % json.dumps(kwargs, ensure_ascii=False))
            # 发送请求
            result = requests.request(**kwargs)

            # 处理cookie
            # set_cookie = requests.utils.dict_from_cookiejar(result.cookies)
            # if set_cookie:
            #     cookie['Cookie'] = set_cookie
            #     self.read.write_yaml_data(cookie)
            #     logs.info("cookie：%s" % cookie)
                
            # 记录响应信息
            logs.info("接口返回信息：%s" % result.text if result.text else result)
            
            # 检查响应状态
            result.raise_for_status()
            
        except requests.exceptions.ConnectionError:
            logs.error("ConnectionError--连接异常")
            pytest.fail("接口请求异常，可能是request的连接数过多或请求速度过快导致程序报错！")
        except requests.exceptions.HTTPError:
            logs.error("HTTPError--http异常")
            pytest.fail("HTTP请求异常，请检查请求URL和参数是否正确！")
        except requests.exceptions.RequestException as e:
            logs.error(e)
            pytest.fail("请求异常，请检查系统或数据是否正常！")
        except Exception as e:
            logs.error(f"未知异常: {str(e)}")
            pytest.fail(f"请求发生未知异常: {str(e)}")
            
        return result

    def run_main(self, name, url, case_name, header, method,encrypt_type=None, cookies=None, file=None, **kwargs):
        """
        接口请求
        :param name: 接口名
        :param url: 接口地址
        :param case_name: 测试用例
        :param header:请求头
        :param method:请求方法
        :param cookies：默认为空
        :param file: 上传文件接口
        :param kwargs: 请求参数，根据yaml文件的参数类型
        :return:
        """

        try:
            data = None
            # 收集报告日志
            logs.info('接口名称：%s' % name)
            logs.info('请求地址：%s' % url)
            logs.info('请求方式：%s' % method)
            logs.info('测试用例名称：%s' % case_name)
            logs.info('请求头：%s' % header)
            logs.info('Cookie：%s' % cookies)
            req_params = json.dumps(kwargs, ensure_ascii=False)
            if "data" in kwargs.keys():
                allure.attach(req_params, '请求参数', allure.attachment_type.TEXT)
                logs.info("请求参数：%s" % kwargs)
            elif "json" in kwargs.keys():
                allure.attach(req_params, '请求参数', allure.attachment_type.TEXT)
                logs.info("请求参数：%s" % kwargs)
            elif "params" in kwargs.keys():
                allure.attach(req_params, '请求参数', allure.attachment_type.TEXT)
                logs.info("请求参数：%s" % kwargs)
        except Exception as e:
            logs.error(e)
        data = kwargs.get('data', None)
        if encrypt_type == 'TC_Base64':
            # 请求前数据
            logs.info('原始请求数据：%s' % json.dumps(kwargs.get('data'), ensure_ascii=False))
            # 如果需要加密请求数据
            data = self.RequestEncryption.encrypt_request_TC_Base64(kwargs.get('data'), self.env_config.get('key'), self.env_config.get('partner_id'))
            # 加密后的请求数据
            logs.info('加密后的请求数据：%s' % json.dumps(data, ensure_ascii=False))
        # time.sleep(0.5)
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
        response = self.send_request(method=method,
                                     url=url,
                                     headers=header,
                                     cookies=cookies,
                                     files=file,
                                     timeout=int(setting.API_TIMEOUT),
                                     verify=False,
                                     data=data)
        return response
