data_default:
  partnerId: "{{partnerId}}"
  autoClaimBizType: "2"
  tradeSerialNo: "{{tradeSerialNo}}"
  policyNo: "{{policyNo}}"
  partnerOrderNo: "{{$string.alphanumeric(length=15)}}"
  partnerInsOrderNo: "{{$string.alphanumeric(length=15)}}"
  reporterName: "产品测试"
  reporterMobile: "***********"
  claimApplyTime: "{{$date.now|addMinutes(0)}}"
  riskTime: "{{$date.now|addMinutes(0)}}"
  selfClaim: "1"
  selfClaimPayTime: "{{$date.now|addMinutes(0)}}"
  selfClaimStatus: "2"
  selfClaimedAmount: "600.00"
  selfClaimedSettleAmount: "600.00"
  selfClaimPayType: "3"
  selfClaimedCouponNo: "{{$string.alphanumeric(length=15)}}"
  paySerialNo: "PAY202310120001"
  payAccount: "acct_123456"
  callbackUrl: "{{returnUrl}}"
  ticketPrice: "1000.00"
  alteredTicketPrice: "800.00"
  ownerOfPayAccount: "1"
  actualLossAmount: "200.00"
  claimCount: "1"
  claimTargetInfoList:
    - claimCauseType: "1"
      actualClaimCauseType: "1"
      vehicleNo: "CA1234"
      trafficStartTime: "{{$date.now|addHours(-2)}}"
      trafficEndTime: "{{$date.now}}"
      actualTrafficStartTime: "{{$date.now}}"
      actualTrafficEndTime: "{{$date.now|addHours(3)}}"
      departure: "北京"
      destination: "上海"
      delayCount: "60"
      elecTicketNo: "ET123456"
      takeoffAirThreeCode: "PEK"
      landAirThreeCode: "SHA"
      passengerNo: "PASS001"
      rideType: "1"
      departProvince: "北京市"
      departCity: "北京市"
      departDistrict: "朝阳区"
      departAddress: "机场路1号"
      destinationProvince: "上海市"
      destinationCity: "上海市"
      destinationDistrict: "浦东新区"
      destinationAddress: "浦东机场路2号"
      remark: "航班取消"
      trafficCompany: "中国国际航空"

wx_data_default:
  partnerId: "{{partnerId}}"
  autoClaimBizType: "1"
  tradeSerialNo: "{{tradeSerialNo}}"
  policyNo: "{{policyNo}}"
  partnerOrderNo: "{{$string.alphanumeric(length=15)}}"
  partnerInsOrderNo: "{{$string.alphanumeric(length=15)}}"
  reporterName: "产品测试"
  reporterMobile: "{{$phone.mobile}}"
  claimApplyTime: "{{$date.now}}"
  riskTime: "{{$date.now}}"
  selfClaim: "0"
  selfClaimPayTime: "{{$date.now}}"
  selfClaimStatus: "2"
  selfClaimedAmount: "63"
  selfClaimedSettleAmount: "63"
  selfClaimPayType: "1"
  selfClaimedCouponNo: "{{$string.alphanumeric(length=15)}}"
  paySerialNo: "{{$string.alphanumeric(length=15)}}"
  payAccount: "acct_123456"
  callbackUrl: "{{returnUrl}}"
  ticketPrice: "1000.00"
  alteredTicketPrice: "800.00"
  ownerOfPayAccount: "2"
  actualLossAmount: "200.00"
  claimCount: "1"
  claimTargetInfoList:
    - claimCauseType: "1"
      actualClaimCauseType: "1"
      vehicleNo: "CA1234"
      trafficStartTime: "{{$date.now|addHours(0)}}"
      trafficEndTime: "{{$date.now}}"
      actualTrafficStartTime: "{{$date.now|addHours(-4)}}"
      actualTrafficEndTime: "{{$date.now|addHours(3)}}"
      departure: "北京"
      destination: "上海"
      delayCount: "60"
      elecTicketNo: "ET123456"
      takeoffAirThreeCode: "PEK"
      landAirThreeCode: "SHA"
      passengerNo: "PASS001"
      rideType: "1"
      departProvince: "北京市"
      departCity: "北京市"
      departDistrict: "朝阳区"
      departAddress: "机场路1号"
      destinationProvince: "上海市"
      destinationCity: "上海市"
      destinationDistrict: "浦东新区"
      destinationAddress: "浦东机场路2号"
      remark: "航班延误60分钟"
      trafficCompany: "中国国际航空"
  claimPayeeInfo:
    payType: "2"
    payerAccountNo: ""
    wechatPayeeInfo:
      openId: "mock-open-id-just-for-test-only"
      userName: ""
      appId: "wx336dcaf6a1ecf632"
    remark: "理赔测试测试推送-微信"

zfb_data_default:
  partnerId: "{{partnerId}}"
  autoClaimBizType: "2"
  tradeSerialNo: "{{tradeSerialNo}}"
  policyNo: "{{policyNo}}"
  partnerOrderNo: "{{$string.alphanumeric(length=15)}}"
  partnerInsOrderNo: "{{$string.alphanumeric(length=15)}}"
  reporterName: "产品测试"
  reporterMobile: "***********"
  claimApplyTime: "{{$date.now|addMinutes(0)}}"
  riskTime: "{{$date.now|addMinutes(0)}}"
  selfClaim: "0"
  selfClaimPayTime: "{{$date.now|addMinutes(0)}}"
  selfClaimStatus: "2"
  selfClaimedAmount: "63"
  selfClaimedSettleAmount: "63"
  selfClaimPayType: "1"
  selfClaimedCouponNo: "{{$string.alphanumeric(length=15)}}"
  paySerialNo: "PAY202310120001"
  payAccount: "acct_123456"
  callbackUrl: "{{returnUrl}}"
  ticketPrice: "1000.00"
  alteredTicketPrice: "800.00"
  ownerOfPayAccount: "2"
  actualLossAmount: "200.00"
  claimCount: "1"
  claimTargetInfoList:
    - claimCauseType: "1"
      actualClaimCauseType: "1"
      vehicleNo: "CA1234"
      trafficStartTime: "{{$date.now|addHours(-2)}}"
      trafficEndTime: "{{$date.now}}"
      actualTrafficStartTime: "{{$date.now}}"
      actualTrafficEndTime: "{{$date.now|addHours(3)}}"
      departure: "北京"
      destination: "上海"
      delayCount: "60"
      elecTicketNo: "ET123456"
      takeoffAirThreeCode: "PEK"
      landAirThreeCode: "SHA"
      passengerNo: "PASS001"
      rideType: "1"
      departProvince: "北京市"
      departCity: "北京市"
      departDistrict: "朝阳区"
      departAddress: "机场路1号"
      destinationProvince: "上海市"
      destinationCity: "上海市"
      destinationDistrict: "浦东新区"
      destinationAddress: "浦东机场路2号"
      remark: "退票赔付"
      trafficCompany: "中国国际航空"
  claimPayeeInfo:
    payType: "1"
    payerAccountNo: ""
    alipayPayeeInfo:
      identity: "<EMAIL>"
      identityType: "ALIPAY_LOGON_ID"
      realName: "hlmnjs7053"
    remark: "理赔测试测试推送"

yhk_data_default:
  partnerId: "{{partnerId}}"
  autoClaimBizType: "{{autoClaimBizType}}"
  tradeSerialNo: "{{tradeSerialNo}}"
  policyNo: "{{policyNo}}"
  partnerOrderNo: "{{$string.alphanumeric(length=15)}}"
  partnerInsOrderNo: "{{$string.alphanumeric(length=15)}}"
  reporterName: "产品测试"
  reporterMobile: "***********"
  claimApplyTime: "{{$date.now|addMinutes(-1)}}"
  riskTime: "{{$date.now|addMinutes(-1)}}"
  selfClaim: "0"
  selfClaimPayTime: "{{$date.now}}"
  selfClaimStatus: "2"
  selfClaimedAmount: "{{ClaimedAmount}}"
  selfClaimedSettleAmount: "{{ClaimedAmount}}"
  selfClaimPayType: "1"
  selfClaimedCouponNo: "{{$string.alphanumeric(length=15)}}"
  paySerialNo: "PAY{{$string.numeric(length=6)}}"
  payAccount: "acct_{{$string.numeric(length=6)}}"
  callbackUrl: "{{returnUrl}}"
  ticketPrice: "1000.00"
  alteredTicketPrice: "800.00"
  ownerOfPayAccount: "2"
  actualLossAmount: "200.00"
  claimCount: "1"
  claimTargetInfoList:
    - claimCauseType: "{{claimCauseType}}"
      actualClaimCauseType: "{{claimCauseType}}"
      vehicleNo: "CA1234"
      trafficStartTime: "{{trafficStartTime}}"
      trafficEndTime: "{{$date.now}}"
      actualTrafficStartTime: "{{actualTrafficStartTime}}"
      actualTrafficEndTime: "{{$date.now|addHours(3)}}"
      departure: "北京"
      destination: "上海"
      delayCount: "60"
      elecTicketNo: "ET123456"
      takeoffAirThreeCode: "PEK"
      landAirThreeCode: "SHA"
      passengerNo: "PASS001"
      rideType: "1"
      departProvince: "北京市"
      departCity: "北京市"
      departDistrict: "朝阳区"
      departAddress: "机场路1号"
      destinationProvince: "上海市"
      destinationCity: "上海市"
      destinationDistrict: "浦东新区"
      destinationAddress: "浦东机场路2号"
      remark: "航班延误60分钟"
      trafficCompany: "中国国际航空"
  claimPayeeInfo:
    payType: "3"
    payerAccountNo: ""
    bankPayeeInfo:
      bankAccount: "****************"
      bankAccountName: "张三"
      provinceCode: "北京市"
      cityCode: "北京市"
      bankName: "中国工商银行朝阳支行"
      phone: "***********"
      remark: "理赔测试"








