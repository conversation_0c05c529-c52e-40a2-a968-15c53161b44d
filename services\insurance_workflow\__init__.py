# -*- coding: utf-8 -*-
"""
保险理赔工作流服务模块
提供完整的保险理赔工作流执行功能
"""

from .core.workflow_service import (
    InsuranceClaimWorkflowService,
    create_workflow_service,
    execute_insurance_claim_workflow
)
from .config.workflow_config import WorkflowConfig, StepConfig, WorkflowConfigManager

__version__ = "1.0.0"
__author__ = "工作流服务开发团队"

# 导出主要的类和函数
__all__ = [
    'InsuranceClaimWorkflowService',
    'create_workflow_service', 
    'execute_insurance_claim_workflow',
    'WorkflowConfig',
    'StepConfig',
    'WorkflowConfigManager'
]
