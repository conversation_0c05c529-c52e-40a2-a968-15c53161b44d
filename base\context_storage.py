import threading
import os
import tempfile
from pathlib import Path
from common.recordlog import logs
import uuid
import json
from common.variable_processor import VariableProcessor
from base.apiutil_business import RequestBase
import allure
import jsonpath
import copy

class context_worker(RequestBase):
    def __init__(self):
        super().__init__()
        # 为每个worker创建独立的存储
        self._local_storage = threading.local()
        self._context_storage = {}  # 使用内存存储替代文件存储
        
    def get_worker_storage_path(self):
        """为当前worker获取独立的存储路径"""
        if not hasattr(self._local_storage, 'worker_id'):
            import multiprocessing
            process_id = os.getpid()
            thread_id = threading.get_ident()
            self._local_storage.worker_id = f"worker_{process_id}_{thread_id}"
            
            # 在项目根目录下创建worker专用目录
            worker_dir = Path('./temp_contexts') / self._local_storage.worker_id
            worker_dir.mkdir(parents=True, exist_ok=True)
            self._local_storage.storage_path = worker_dir
            
            logs.debug(f"创建worker存储目录: {worker_dir}")
            
        return self._local_storage.storage_path

    def create_workflow_context(self):
        """
        为工作流创建独立的上下文环境，避免并行测试时变量冲突
        :return: 上下文ID
        """
        worker_id = self.get_worker_storage_path().name
        context_id = f"{worker_id}_{str(uuid.uuid4())[:8]}"
        # 初始化上下文存储
        if context_id not in self._context_storage:
            self._context_storage[context_id] = {}
            
        logs.info(f"创建工作流上下文: {context_id}")
        return context_id

    def get_context_variable(self, context_id, key):
        """
        从上下文中获取变量（支持跨进程读取）
        :param context_id: 上下文ID
        :param key: 变量key
        :return: 变量值
        """
        try:
            # 首先尝试从内存获取
            context_data = self._context_storage.get(context_id, {})
            value = context_data.get(key)
            
            # 如果内存中没有找到，且上下文ID不属于当前worker，尝试从文件加载
            if value is None and not context_id.startswith(self.get_worker_storage_path().name):
                logs.debug(f"尝试从文件加载跨进程上下文: {context_id}")
                context_data = self._load_context_from_file_cross_process(context_id)
                if context_data:
                    # 加载到内存中以提高后续访问效率
                    self._context_storage[context_id] = context_data
                    value = context_data.get(key)
                    logs.debug(f"从跨进程文件加载到变量 {key}: {value}")
            
            logs.debug(f"从上下文 {context_id} 获取变量 {key}: {value}")
            return value
        except Exception as e:
            logs.error(f"获取上下文变量失败: {e}")
            return None

    def set_context_variable(self, context_id, data_dict):
        """
        设置上下文变量（使用内存存储）
        :param context_id: 上下文ID
        :param data_dict: 变量字典
        """
        try:
            if context_id not in self._context_storage:
                self._context_storage[context_id] = {}
            
            self._context_storage[context_id].update(data_dict)
            logs.info(f"设置上下文 {context_id} 变量: {data_dict}")
            
            # 可选：同时保存到worker专用文件作为备份
            self._save_context_to_file(context_id)
            
        except Exception as e:
            logs.error(f"设置上下文变量失败: {e}")

    def _save_context_to_file(self, context_id):
        """将上下文数据保存到worker专用文件"""
        try:
            worker_storage = self.get_worker_storage_path()
            context_file = worker_storage / f"context_{context_id}.json"
            
            context_data = self._context_storage.get(context_id, {})
            with open(context_file, 'w', encoding='utf-8') as f:
                json.dump(context_data, f, ensure_ascii=False, indent=2)
                
            logs.debug(f"保存上下文到文件: {context_file}")
                
        except Exception as e:
            logs.warning(f"保存上下文到文件失败: {e}")

    def _load_context_from_file(self, context_id):
        """从worker专用文件加载上下文数据"""
        try:
            worker_storage = self.get_worker_storage_path()
            context_file = worker_storage / f"context_{context_id}.json"
            
            if context_file.exists():
                with open(context_file, 'r', encoding='utf-8') as f:
                    context_data = json.load(f) or {}
                    self._context_storage[context_id] = context_data
                    return context_data
        except Exception as e:
            logs.warning(f"从文件加载上下文失败: {e}")        
        return {}

    def _load_context_from_file_cross_process(self, context_id):
        """从其他进程保存的文件中加载上下文数据"""
        try:
            # 查找所有可能的worker目录 - 使用项目根目录绝对路径
            import os
            project_root = Path(os.getcwd())
            temp_contexts_dir = project_root / "temp_contexts"
            logs.debug(f"查找跨进程上下文目录: {temp_contexts_dir}")
            
            if not temp_contexts_dir.exists():
                logs.debug(f"跨进程上下文目录不存在: {temp_contexts_dir}")
                return {}
            
            # 智能匹配worker目录：从context_id中提取worker前缀
            # context_id格式: worker_进程ID_线程ID_uuid
            target_worker_prefix = None
            if '_' in context_id:
                # 尝试从context_id中提取worker前缀（去掉最后的UUID部分）
                parts = context_id.split('_')
                if len(parts) >= 4 and parts[0] == 'worker':
                    # worker_进程ID_线程ID_uuid -> worker_进程ID_线程ID
                    target_worker_prefix = '_'.join(parts[:-1])
                    logs.debug(f"从context_id {context_id} 提取worker前缀: {target_worker_prefix}")
                    
                    # 查找对应的worker目录
                    target_worker_dir = temp_contexts_dir / target_worker_prefix
                    if target_worker_dir.exists():
                        context_file = target_worker_dir / f"context_{context_id}.json"
                        logs.debug(f"智能匹配worker目录，尝试读取: {context_file}")
                        if context_file.exists():
                            with open(context_file, 'r', encoding='utf-8') as f:
                                context_data = json.load(f) or {}
                                logs.info(f"从跨进程文件智能匹配加载上下文 {context_id}: {context_data}")
                                return context_data
                        else:
                            logs.debug(f"智能匹配的上下文文件不存在: {context_file}")
                    else:
                        logs.debug(f"智能匹配的worker目录不存在: {target_worker_dir}")
            
            # 如果智能匹配失败，则遍历所有worker目录寻找
            logs.debug("智能匹配失败，遍历所有worker目录")
            for worker_dir in temp_contexts_dir.iterdir():
                if worker_dir.is_dir() and worker_dir.name.startswith('worker_'):
                    context_file = worker_dir / f"context_{context_id}.json"
                    logs.debug(f"遍历检查跨进程上下文文件: {context_file}")
                    if context_file.exists():
                        with open(context_file, 'r', encoding='utf-8') as f:
                            context_data = json.load(f) or {}
                            logs.info(f"从跨进程文件遍历加载上下文 {context_id}: {context_data}")
                            return context_data
        except Exception as e:
            logs.warning(f"从跨进程文件加载上下文失败: {e}")
        
        return {}

    def cleanup_worker_context(self):
        """清理当前worker的上下文数据"""
        try:
            # 清理内存中的数据
            worker_storage = self.get_worker_storage_path()
            worker_prefix = worker_storage.name
            
            # 移除该worker的所有上下文
            contexts_to_remove = [ctx_id for ctx_id in self._context_storage.keys() 
                                if ctx_id.startswith(worker_prefix)]
            
            for ctx_id in contexts_to_remove:
                del self._context_storage[ctx_id]
            
            # 清理临时文件
            import shutil
            if worker_storage.exists():
                shutil.rmtree(worker_storage, ignore_errors=True)
                
            logs.debug(f"清理worker上下文完成: {worker_prefix}")
            
        except Exception as e:
            logs.warning(f"清理worker上下文失败: {e}")

    def replace_load_with_context(self, data, context_id, fallback_context_id=None):
        """
        支持上下文的变量替换（使用内存存储），支持fallback上下文
        :param data: 待替换的数据
        :param context_id: 主要上下文ID
        :param fallback_context_id: 备用上下文ID，当主要上下文中找不到变量时使用
        :return: 替换后的数据
        """
        if not data:
            return data
            
        try:
            # 确保数据能正确序列化
            if isinstance(data, str):
                str_data = data
            else:
                str_data = json.dumps(data, ensure_ascii=False)
            
            # 处理上下文变量 {{variable_name}}
            import re
            context_pattern = r'\{\{(\w+)\}\}'
            context_matches = re.findall(context_pattern, str_data)
            
            for var_name in context_matches:
                var_value = None
                
                # 首先尝试从主要上下文获取变量
                var_value = self.get_context_variable(context_id, var_name)
                
                # 如果主要上下文中没有找到，且有fallback上下文，则尝试从fallback上下文获取
                if var_value is None and fallback_context_id:
                    var_value = self.get_context_variable(fallback_context_id, var_name)
                    if var_value is not None:
                        logs.info(f"从fallback上下文 {fallback_context_id} 获取变量: {var_name} = {var_value}")
                
                if var_value is not None:
                    str_data = str_data.replace('{{' + var_name + '}}', str(var_value))
                    logs.debug(f"上下文变量替换: {var_name} -> {var_value} (上下文: {context_id})")
                else:
                    logs.warning(f"上下文变量 {var_name} 在上下文 {context_id} 和 fallback {fallback_context_id} 中都未找到")
            
            # 再进行常规的变量替换
            if isinstance(data, str):
                final_data = self.replace_load(str_data)
            else:
                final_data = self.replace_load(json.loads(str_data))
            final_data = VariableProcessor.process_json_data(final_data)
            return final_data
        except Exception as e:
            error_msg = f"上下文变量替换失败: {e}"
            logs.error(error_msg)
            logs.error(f"原始数据: {data}")
            logs.error(f"上下文ID: {context_id}, fallback: {fallback_context_id}")
            allure.attach(error_msg, '变量替换错误', allure.attachment_type.TEXT)
            return data

    def replace_load_with_context_legacy(self, data, context_id):
        """
        支持上下文的变量替换（使用内存存储）
        :param data: 待替换的数据
        :param context_id: 上下文ID
        :return: 替换后的数据
        """
        if not data:
            return data
            
        try:
            # 确保数据能正确序列化
            if isinstance(data, str):
                str_data = data
            else:
                str_data = json.dumps(data, ensure_ascii=False)
            
            # 处理上下文变量 {{variable_name}}
            import re
            context_pattern = r'\{\{(\w+)\}\}'
            context_matches = re.findall(context_pattern, str_data)
            
            for var_name in context_matches:
                var_value = self.get_context_variable(context_id, var_name)
                if var_value is not None:
                    str_data = str_data.replace('{{' + var_name + '}}', str(var_value))
                    logs.debug(f"上下文变量替换: {var_name} -> {var_value}")
                else:
                    logs.warning(f"上下文变量 {var_name} 未找到")
            
            # 再进行常规的变量替换
            if isinstance(data, str):
                final_data = self.replace_load(str_data)
            else:
                final_data = self.replace_load(json.loads(str_data))
            final_data = VariableProcessor.process_json_data(final_data)
            return final_data
        except Exception as e:
            error_msg = f"上下文变量替换失败: {e}"
            logs.error(error_msg)
            logs.error(f"原始数据: {data}")
            logs.error(f"上下文ID: {context_id}")
            allure.attach(error_msg, '变量替换错误', allure.attachment_type.TEXT)
            return data

    def specification_yaml_with_context(self, base_info, testcase, context_id):
        """
        支持上下文的YAML测试用例执行
        """
        try:
            # 深拷贝避免修改原始数据
            tc = copy.deepcopy(testcase)
            extract = tc.pop('extract', None)
            
            logs.info(f"执行接口调用 - 上下文ID: {context_id}")
            
            # 执行接口调用
            result = self.specification_yaml(base_info, tc)
            
            # 如果有提取配置，使用上下文存储
            if extract and result:
                try:
                    response_text = result.text
                    extract_data = {}
                    
                    logs.info(f"开始提取上下文变量: {extract}")
                    
                    for key, value in extract.items():
                        if "$" in value:
                            try:
                                response_json = json.loads(response_text)
                                ext_json = jsonpath.jsonpath(response_json, value)
                                if ext_json:
                                    extract_data[key] = ext_json[0]
                                    logs.info(f"成功提取变量: {key} = {ext_json[0]}")
                                    allure.attach(str(ext_json[0]), f'{key}提取到的参数:{ext_json[0]}', allure.attachment_type.TEXT)
                                else:
                                    extract_data[key] = "未提取到数据，该接口返回结果可能为空"
                                    logs.warning(f"未能提取变量: {key}, 表达式: {value}")
                            except json.JSONDecodeError as jde:
                                logs.error(f"JSON解析失败: {jde}")
                            except Exception as ee:
                                logs.error(f"提取变量 {key} 时发生错误: {ee}")
                    
                    # 存储到上下文中
                    if extract_data:
                        self.set_context_variable(context_id, extract_data)
                        logs.info(f'上下文{context_id}提取到参数：{extract_data}')
                        
                except Exception as e:
                    error_msg = f'上下文变量提取异常：{e}'
                    logs.error(error_msg)
                    allure.attach(error_msg, '变量提取错误', allure.attachment_type.TEXT)
            
            return result
        except Exception as e:
            error_msg = f"执行带上下文的YAML测试用例失败: {e}"
            logs.error(error_msg)
            allure.attach(error_msg, 'YAML执行错误', allure.attachment_type.TEXT)
            raise e
