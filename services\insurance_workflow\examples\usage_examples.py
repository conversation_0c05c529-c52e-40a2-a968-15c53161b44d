# -*- coding: utf-8 -*-
"""
保险理赔工作流服务使用示例
展示如何在不同测试类中调用封装的功能
"""

import pytest
import allure
from typing import List

from services.insurance_workflow.core.workflow_service import (
    InsuranceClaimWorkflowService,
    create_workflow_service,
    execute_insurance_claim_workflow
)
from services.insurance_workflow.config.workflow_config import StepConfig


class TestInsuranceWorkflowExamples:
    """保险工作流测试示例类"""
    
    def setup_class(self):
        """类级别的setup，初始化工作流服务"""
        self.workflow_service = create_workflow_service()
    
    def teardown_class(self):
        """类级别的teardown，清理资源"""
        self.workflow_service.cleanup_all_contexts()
    
    @allure.epic('保险项目')
    @allure.feature('工作流服务示例')
    @allure.story('完整工作流执行示例')
    def test_complete_workflow_from_yaml(self):
        """
        示例1: 从YAML文件执行完整的保险理赔工作流
        这是最常用的方式，适合标准的工作流测试
        """
        # 执行工作流
        results = self.workflow_service.execute_workflow_from_yaml(
            'testcase/DeliveryService/insureInvoce copy.yml'
        )
        
        # 验证执行结果
        assert len(results) > 0, "应该至少有一个工作流执行结果"
        
        for result in results:
            assert result.success, f"工作流 {result.workflow_name} 执行失败: {result.error_message}"
            assert len(result.step_results) > 0, "应该有步骤执行结果"
            
            # 检查每个步骤的执行结果
            for step_result in result.step_results:
                if not step_result.success:
                    pytest.fail(f"步骤执行失败: {step_result.error}")
    
    @allure.epic('保险项目')
    @allure.feature('工作流服务示例')
    @allure.story('单步骤执行示例')
    def test_single_step_execution(self):
        """
        示例2: 执行单个步骤
        适合单独测试某个API接口或调试特定步骤
        """
        # 定义单个步骤配置
        step_config = {
            'step_name': '投保接口测试',
            'api_config': {
                'api_name': '投保接口',
                'url': '/api/scene/insure',
                'method': 'post',
                'header': {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                'encrypt_type': 'TC_Base64'
            },
            'data': {
                'planCode': '00200005003',
                'effectTime': '{{$date.now|addMinutes(-1)}}',
                'expiryTime': '{{$date.now|addDays(1)}}',
                'totalPremium': '50',
                'sumAssured': '62000990'
            },
            'validation': [
                {'contains': {'status_code': 200}},
                {'eq': {'$.respCode': '0'}}
            ],
            'extract': {
                'tradeSerialNo': '$.insureData.tradeSerialNo',
                'serialNo': '$.insureData.serialNo'
            },
            'max_retry': 3,
            'retry_interval': 1
        }
        
        # 执行单个步骤
        step_result, context_id = self.workflow_service.execute_single_step(step_config)
        
        # 验证执行结果
        assert step_result.success, f"步骤执行失败: {step_result.error}"
        assert context_id is not None, "应该返回上下文ID"
        
        # 获取提取的变量
        variables = self.workflow_service.get_context_variables(context_id)
        assert 'tradeSerialNo' in variables, "应该提取到tradeSerialNo"
        assert 'serialNo' in variables, "应该提取到serialNo"
        
        # 清理上下文
        self.workflow_service.cleanup_context(context_id)
    
    @allure.epic('保险项目')
    @allure.feature('工作流服务示例')
    @allure.story('上下文变量管理示例')
    def test_context_variable_management(self):
        """
        示例3: 上下文变量管理
        展示如何在步骤间传递和使用变量
        """
        # 创建工作流上下文
        context_id = self.workflow_service.context_manager.create_workflow_context(
            "变量管理测试", 0
        )
        
        # 设置上下文变量
        success = self.workflow_service.set_context_variable(context_id, 'testVar', 'testValue')
        assert success, "设置上下文变量应该成功"
        
        # 批量设置变量
        variables = {
            'tradeSerialNo': 'TEST123456',
            'serialNo': 'SN789012',
            'policyNo': 'POL345678'
        }
        self.workflow_service.context_manager.set_variables(context_id, variables)
        
        # 获取变量
        all_variables = self.workflow_service.get_context_variables(context_id)
        assert 'testVar' in all_variables
        assert all_variables['testVar'] == 'testValue'
        assert 'tradeSerialNo' in all_variables
        
        # 在数据中使用变量
        test_data = {
            'tradeSerialNo': '{{tradeSerialNo}}',
            'serialNo': '{{serialNo}}',
            'newField': 'staticValue'
        }
        
        processed_data = self.workflow_service.context_manager.replace_variables_in_data(
            context_id, test_data
        )
        
        assert processed_data['tradeSerialNo'] == 'TEST123456'
        assert processed_data['serialNo'] == 'SN789012'
        assert processed_data['newField'] == 'staticValue'
        
        # 清理上下文
        self.workflow_service.cleanup_context(context_id)
    
    @allure.epic('保险项目')
    @allure.feature('工作流服务示例')
    @allure.story('便捷函数使用示例')
    def test_convenience_function(self):
        """
        示例4: 使用便捷函数
        最简单的使用方式，适合快速执行工作流
        """
        # 使用便捷函数执行工作流
        results = execute_insurance_claim_workflow(
            'testcase/DeliveryService/insureInvoce copy.yml'
        )
        
        # 验证结果
        assert len(results) > 0, "应该有执行结果"
        
        for result in results:
            print(f"工作流: {result.workflow_name}")
            print(f"执行状态: {'成功' if result.success else '失败'}")
            print(f"执行时间: {result.execution_time:.2f}秒")
            print(f"步骤数量: {len(result.step_results)}")
            
            if not result.success:
                print(f"错误信息: {result.error_message}")


class TestCustomWorkflow:
    """自定义工作流测试类示例"""
    
    def setup_method(self):
        """方法级别的setup"""
        self.workflow_service = InsuranceClaimWorkflowService()
    
    def teardown_method(self):
        """方法级别的teardown"""
        self.workflow_service.cleanup_all_contexts()
    
    @allure.epic('保险项目')
    @allure.feature('自定义工作流')
    @allure.story('理赔流程测试')
    def test_claim_process_only(self):
        """
        示例5: 只测试理赔流程
        跳过投保步骤，直接测试理赔
        """
        # 创建上下文并设置必要的变量
        context_id = self.workflow_service.context_manager.create_workflow_context(
            "理赔流程测试", 0
        )
        
        # 设置理赔所需的前置变量（通常来自投保步骤）
        claim_variables = {
            'tradeSerialNo': 'MOCK_TRADE_123456',
            'serialNo': 'MOCK_SERIAL_789012',
            'policyNo': 'MOCK_POLICY_345678'
        }
        self.workflow_service.context_manager.set_variables(context_id, claim_variables)
        
        # 定义理赔步骤配置
        claim_step_config = {
            'step_name': '申请理赔',
            'api_config': {
                'api_name': '申请理赔接口',
                'url': '/api/autoclaim/applyclaim',
                'method': 'post',
                'header': {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                'encrypt_type': 'TC_Base64'
            },
            'data': {
                'autoClaimBizType': '5',
                'selfClaimedSettleAmount': '700',
                'selfClaimedAmount': '700',
                'payType': '1',
                'tradeSerialNo': '{{tradeSerialNo}}',
                'policyNo': '{{policyNo}}',
                'claimTargetInfoList': [
                    {
                        'claimCauseType': '6',
                        'actualClaimCauseType': '6',
                        'trafficStartTime': '{{$date.now|addHours(-2)}}',
                        'actualTrafficStartTime': '{{$date.now}}'
                    }
                ]
            },
            'validation': [
                {'contains': {'status_code': 200}},
                {'eq': {'$.respCode': '0'}}
            ],
            'max_retry': 3,
            'retry_interval': 2
        }
        
        # 执行理赔步骤
        step_result, _ = self.workflow_service.execute_single_step(
            claim_step_config, context_id
        )
        
        # 验证执行结果
        assert step_result.success, f"理赔步骤执行失败: {step_result.error}"
        
        # 清理上下文
        self.workflow_service.cleanup_context(context_id)


# 独立函数示例
def run_insurance_workflow_standalone():
    """
    示例6: 独立函数中使用工作流服务
    适合在非测试环境中使用
    """
    try:
        # 创建服务实例
        service = create_workflow_service()
        
        # 执行工作流
        results = service.execute_workflow_from_yaml(
            'testcase/DeliveryService/insureInvoce copy.yml'
        )
        
        # 处理结果
        for result in results:
            if result.success:
                print(f"✅ 工作流 {result.workflow_name} 执行成功")
                print(f"   执行时间: {result.execution_time:.2f}秒")
                print(f"   步骤数量: {len(result.step_results)}")
                
                # 获取最终的上下文变量
                final_variables = service.get_context_variables(result.context_id)
                print(f"   最终变量: {final_variables}")
            else:
                print(f"❌ 工作流 {result.workflow_name} 执行失败")
                print(f"   错误信息: {result.error_message}")
        
        # 清理资源
        service.cleanup_all_contexts()
        
        return results
        
    except Exception as e:
        print(f"执行工作流时发生错误: {e}")
        return None


if __name__ == "__main__":
    # 直接运行示例
    run_insurance_workflow_standalone()
