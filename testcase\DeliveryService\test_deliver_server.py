import allure
import pytest

from common.readyaml import get_testcase_yaml
from base.apiutil_business import RequestBase
from base.generateId import m_id, c_id
from common.readyaml import ReadYamlData


# 注意：业务场景的接口测试要调用base目录下的apiutil_business文件
@allure.epic('保险项目')
@allure.feature(next(m_id) + '投递服务')
@allure.parent_suite('保险项目')
@allure.suite('投递服务')
@allure.sub_suite('投递服务接口')
class TestDeliverServer:
    @pytest.mark.run(order=1)
    @allure.story(next(c_id) + '产品基础信息查询')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/product_basic.yml'))
    def test_product_basic(self, base_info, testcase):
        allure.dynamic.title(testcase['case_name'])
        allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
        RequestBase().specification_yaml(base_info, testcase)

    @pytest.mark.run(order=2)
    @allure.story(next(c_id) + '投保接口')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/insurance.yml'))
    def test_insurance(self, base_info, testcase):
        allure.dynamic.title(testcase['case_name'])
        allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
        RequestBase().specification_yaml(base_info, testcase)

    # @pytest.mark.run(order=3)
    # @allure.story(next(c_id) + '投保查询接口')
    # @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/insuranceQuery.yaml'))
    # def test_insurance_query(self, base_info, testcase):
    #     allure.dynamic.title(testcase['case_name'])
    #     allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
    #     RequestBase().specification_yaml(base_info, testcase)

    @pytest.mark.run(order=4)
    @allure.story(next(c_id) + '开票接口')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/invoice.yml'))
    def test_invoice(self, base_info, testcase):
        allure.dynamic.title(testcase['case_name'])
        allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
        RequestBase().specification_yaml(base_info, testcase)

    @pytest.mark.run(order=5)
    @allure.story(next(c_id) + '开票查询接口')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/invoiceQuery.yml'))
    def test_invoice_query(self, base_info, testcase):
        allure.dynamic.title(testcase['case_name'])
        allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
        RequestBase().specification_yaml(base_info, testcase)

    @pytest.mark.run(order=6)
    @allure.story(next(c_id) + '理赔接口')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/applyclaim.yml'))
    def test_apply_claim(self, base_info, testcase):
        allure.dynamic.title(testcase['case_name'])
        allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
        RequestBase().specification_yaml(base_info, testcase)

    @pytest.mark.run(order=7)
    @allure.story(next(c_id) + '作废发票接口')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/invalidatedInvoice.yml'))
    def test_invalidated_invoice(self, base_info, testcase):
        allure.dynamic.title(testcase['case_name'])
        allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
        RequestBase().specification_yaml(base_info, testcase)
    # 接口关联目前还未实现！！！等待后续实现
    @pytest.mark.run(order=1)
    @allure.epic('保险项目')
    @allure.feature('投递服务')
    @allure.story('产品基础信息查询和投保')
    @pytest.mark.parametrize('base_info,testcase', get_testcase_yaml('./testcase/DeliveryService/product_basic.yml'))
    def test_product_basic_and_insurance(self, base_info, testcase):
        # 设置测试用例分组
        product_case_name = testcase['case_name']
        allure.dynamic.suite(f"产品测试 - {product_case_name}")
        
        with allure.step(f"执行产品基础信息查询"):
            # 执行产品基础信息查询
            allure.dynamic.title(f"产品基础信息查询 - {product_case_name}")
            allure.attach(str(testcase), '请求参数', allure.attachment_type.TEXT)
            RequestBase().specification_yaml(base_info, testcase)
            
            # 获取提取的数据
            yaml_reader = ReadYamlData()
            fix_premium = yaml_reader.get_extract_yaml('fixPremium')
            sum_assured = yaml_reader.get_extract_yaml('sumAssured')
            plan_code = yaml_reader.get_extract_yaml('planCode')
            
            # 记录提取的数据
            allure.attach(f"保费: {fix_premium}\n保额: {sum_assured}\n计划代码: {plan_code}", 
                        '提取的数据', allure.attachment_type.TEXT)
        
        # 读取投保接口的测试用例
        insurance_cases = get_testcase_yaml('./testcase/DeliveryService/insurance.yml')
        
        for insurance_base_info, insurance_testcase in insurance_cases:
            with allure.step(f"执行投保接口测试"):
                # 更新投保测试用例数据
                if insurance_testcase.get('data'):
                    insurance_testcase['data'].update({
                        'fixPremium': fix_premium,
                        'sumAssured': sum_assured,
                        'planCode': plan_code
                    })
                
                # 设置投保测试用例的标题
                insurance_case_name = insurance_testcase['case_name']
                allure.dynamic.title(f"投保接口 - {product_case_name} - {insurance_case_name}")
                
                # 记录测试数据
                allure.attach(str(insurance_testcase), '投保接口请求参数', allure.attachment_type.TEXT)
                
                # 执行投保接口测试
                RequestBase().specification_yaml(insurance_base_info, insurance_testcase)