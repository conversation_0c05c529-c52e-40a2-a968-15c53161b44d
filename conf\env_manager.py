import yaml
import os
from conf.setting import DEFAULT_ENV

class EnvManager:
    def __init__(self):
        self.env_file = os.path.join(os.path.dirname(__file__), 'environments.yaml')
        self.current_env = DEFAULT_ENV
        self.environments = self._load_environments()
    
    def _load_environments(self):
        """加载环境配置"""
        with open(self.env_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)['environments']
    
    def get_current_env_config(self):
        """获取当前环境配置"""
        return self.environments.get(self.current_env, {})
    
    def set_environment(self, env_name):
        """设置当前环境"""
        if env_name in self.environments:
            self.current_env = env_name
            return True
        return False
    
    def get_available_environments(self):
        """获取可用环境列表"""
        return list(self.environments.keys())

# 创建环境管理器实例
env_manager = EnvManager() 