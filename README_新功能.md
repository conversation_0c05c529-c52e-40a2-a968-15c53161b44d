# 工作流测试框架 - 跳过执行和依赖等待功能

## 新增功能概述

本次更新为工作流测试框架新增了两个重要功能：

### 1. skip_execution - 跳过执行功能

**作用**: 允许在测试用例中标记某些步骤跳过执行，同时智能跳过相关的通用步骤。

**使用方法**:
```yaml
data:
  - caseName: "正常执行的用例"
    planCode: "00200005003"
    
  - caseName: "跳过执行的用例" 
    skip_execution: true  # 添加此标记跳过执行
    planCode: "00200005004"
```

**智能跳过**: 当标记跳过的步骤后面紧跟通用接口时，通用接口也会自动跳过。

### 2. Forwardsign - 依赖等待功能

**作用**: 实现步骤间的依赖控制，某个步骤可以等待其他步骤完成后再执行，**并且可以获取被等待步骤的上下文数据进行变量替换**。

**使用方法**:
```yaml
data:
  - Forwardsign: "case:0,step:2"  # 等待用例0的步骤2完成
    autoClaimBizType: "5"
    policyNo: "{{policyNo}}"     # 将从用例0步骤2的上下文中获取
    # 其他业务数据...
```

**上下文变量传递**: 当步骤等待依赖完成后，会自动获取被等待步骤的上下文ID，优先使用该上下文中的变量进行替换。

**Fallback机制**: 如果依赖步骤的上下文中没有找到某个变量，会自动从当前步骤的上下文中查找。

**格式说明**:
- `case:X` - X为用例索引（从0开始）
- `step:Y` - Y为步骤索引（从0开始）
- 必须严格按照 `case:数字,step:数字` 格式

## 应用场景

### skip_execution 适用场景
- 🚫 **调试测试**: 临时跳过有问题的测试用例
- 🌍 **环境限制**: 某些环境不支持特定业务场景
- 📊 **分阶段测试**: 分批次执行不同的测试用例组合

### Forwardsign 适用场景
- 🔄 **顺序控制**: 确保测试步骤按特定顺序执行
- 📈 **资源依赖**: 后续步骤依赖前置步骤的结果
- 🎯 **复杂流程**: 多分支业务流程的精确控制
- 🔗 **数据传递**: 跨用例的变量传递和数据共享

## 完整示例

参考文件: `testcase/DeliveryService/skip_and_dependency_example.yml`

```yaml
workflow_testcases:
  - workflow_name: "投保-理赔-多数据组测试"
    steps:
      - step_name: "投保"
        data:
          - caseName: "正常投保"
            planCode: "00200005003"
          - caseName: "跳过投保"
            skip_execution: true    # 跳过此用例
            planCode: "00200005004"
              - step_name: "状态查询"  
        data:
          - tradeSerialNo: "{{tradeSerialNo}}"  # 来自本用例的投保步骤
          - Forwardsign: "case:0,step:0"        # 等待第一个投保完成
            tradeSerialNo: "{{tradeSerialNo}}"  # 来自用例0步骤0的上下文
            serialNo: "{{serialNo}}"            # 来自用例0步骤0的上下文
```

**变量替换优先级**:
1. 🥇 **依赖上下文**: 优先从Forwardsign指定的步骤上下文获取变量
2. 🥈 **当前上下文**: 如果依赖上下文中没有，则从当前步骤上下文获取
3. 🥉 **全局变量**: 最后使用框架全局变量和函数

## 技术特性

✅ **线程安全**: 支持并发测试场景
✅ **智能跳过**: 自动识别和跳过通用步骤  
✅ **超时控制**: 默认5分钟等待超时
✅ **详细日志**: 完整的执行和跳过记录
✅ **错误处理**: 格式验证和异常处理
✅ **上下文传递**: 依赖步骤间的变量自动传递
✅ **Fallback机制**: 多层级变量查找策略

## 运行测试

```bash
# 运行包含新功能的测试用例
python run.py -k test_deliver_workflow

# 运行示例测试文件
pytest testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow -v
```

## 详细文档

📖 查看完整文档: `docs/工作流跳过和依赖功能说明.md`

---

**注意**: 这些功能完全向后兼容，现有测试用例无需修改即可正常运行。
