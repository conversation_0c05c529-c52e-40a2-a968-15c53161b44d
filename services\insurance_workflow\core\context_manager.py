# -*- coding: utf-8 -*-
"""
工作流上下文管理器
提供工作流执行过程中的上下文数据管理功能
"""

import threading
import os
import json
import uuid
import time
import re
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field


@dataclass
class WorkflowContext:
    """工作流上下文数据结构"""
    context_id: str
    workflow_name: str
    case_index: int = 0
    variables: Dict[str, Any] = field(default_factory=dict)
    step_results: List[Dict] = field(default_factory=list)
    created_time: float = field(default_factory=time.time)
    last_updated: float = field(default_factory=time.time)


class WorkflowContextManager:
    """工作流上下文管理器"""
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化上下文管理器
        :param project_root: 项目根目录路径
        """
        if project_root is None:
            project_root = os.getcwd()
        
        self.project_root = Path(project_root)
        self.temp_dir = self.project_root / "temp_contexts"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 线程本地存储
        self._local_storage = threading.local()
        # 内存中的上下文存储
        self._context_storage: Dict[str, WorkflowContext] = {}
        # 线程锁
        self._lock = threading.Lock()
    
    def get_worker_id(self) -> str:
        """获取当前工作线程ID"""
        if not hasattr(self._local_storage, 'worker_id'):
            process_id = os.getpid()
            thread_id = threading.get_ident()
            self._local_storage.worker_id = f"worker_{process_id}_{thread_id}"
        return self._local_storage.worker_id
    
    def create_workflow_context(self, workflow_name: str, case_index: int = 0) -> str:
        """
        创建工作流上下文
        :param workflow_name: 工作流名称
        :param case_index: 用例索引
        :return: 上下文ID
        """
        worker_id = self.get_worker_id()
        context_id = f"{worker_id}_{str(uuid.uuid4())[:8]}"
        
        context = WorkflowContext(
            context_id=context_id,
            workflow_name=workflow_name,
            case_index=case_index
        )
        
        with self._lock:
            self._context_storage[context_id] = context
        
        # 保存到文件作为备份
        self._save_context_to_file(context)
        
        return context_id
    
    def get_context(self, context_id: str) -> Optional[WorkflowContext]:
        """
        获取工作流上下文
        :param context_id: 上下文ID
        :return: 工作流上下文对象
        """
        with self._lock:
            context = self._context_storage.get(context_id)
            
            # 如果内存中没有，尝试从文件加载
            if context is None:
                context = self._load_context_from_file(context_id)
                if context:
                    self._context_storage[context_id] = context
            
            return context
    
    def set_variable(self, context_id: str, key: str, value: Any) -> bool:
        """
        设置上下文变量
        :param context_id: 上下文ID
        :param key: 变量名
        :param value: 变量值
        :return: 是否设置成功
        """
        context = self.get_context(context_id)
        if context is None:
            return False
        
        with self._lock:
            context.variables[key] = value
            context.last_updated = time.time()
        
        # 保存到文件
        self._save_context_to_file(context)
        return True
    
    def get_variable(self, context_id: str, key: str, default: Any = None) -> Any:
        """
        获取上下文变量
        :param context_id: 上下文ID
        :param key: 变量名
        :param default: 默认值
        :return: 变量值
        """
        context = self.get_context(context_id)
        if context is None:
            return default
        
        return context.variables.get(key, default)
    
    def set_variables(self, context_id: str, variables: Dict[str, Any]) -> bool:
        """
        批量设置上下文变量
        :param context_id: 上下文ID
        :param variables: 变量字典
        :return: 是否设置成功
        """
        context = self.get_context(context_id)
        if context is None:
            return False
        
        with self._lock:
            context.variables.update(variables)
            context.last_updated = time.time()
        
        # 保存到文件
        self._save_context_to_file(context)
        return True
    
    def add_step_result(self, context_id: str, step_name: str, result: Dict[str, Any]) -> bool:
        """
        添加步骤执行结果
        :param context_id: 上下文ID
        :param step_name: 步骤名称
        :param result: 执行结果
        :return: 是否添加成功
        """
        context = self.get_context(context_id)
        if context is None:
            return False
        
        step_result = {
            'step_name': step_name,
            'timestamp': time.time(),
            'result': result
        }
        
        with self._lock:
            context.step_results.append(step_result)
            context.last_updated = time.time()
        
        # 保存到文件
        self._save_context_to_file(context)
        return True
    
    def replace_variables_in_data(self, context_id: str, data: Any, 
                                  fallback_context_id: Optional[str] = None) -> Any:
        """
        在数据中替换上下文变量
        :param context_id: 主要上下文ID
        :param data: 待替换的数据
        :param fallback_context_id: 备用上下文ID
        :return: 替换后的数据
        """
        if not data:
            return data
        
        try:
            # 确保数据能正确序列化
            if isinstance(data, str):
                str_data = data
            else:
                str_data = json.dumps(data, ensure_ascii=False)
            
            # 处理上下文变量 {{variable_name}}
            context_pattern = r'\{\{(\w+)\}\}'
            context_matches = re.findall(context_pattern, str_data)
            
            for var_name in context_matches:
                var_value = None
                
                # 首先尝试从主要上下文获取变量
                var_value = self.get_variable(context_id, var_name)
                
                # 如果主要上下文中没有找到，且有fallback上下文，则尝试从fallback上下文获取
                if var_value is None and fallback_context_id:
                    var_value = self.get_variable(fallback_context_id, var_name)
                
                if var_value is not None:
                    str_data = str_data.replace('{{' + var_name + '}}', str(var_value))
            
            # 还原数据类型
            if isinstance(data, str):
                return str_data
            else:
                return json.loads(str_data)
                
        except Exception as e:
            # 如果替换失败，返回原始数据
            return data
    
    def cleanup_context(self, context_id: str) -> bool:
        """
        清理指定的上下文
        :param context_id: 上下文ID
        :return: 是否清理成功
        """
        try:
            with self._lock:
                if context_id in self._context_storage:
                    del self._context_storage[context_id]
            
            # 删除文件
            context_file = self.temp_dir / f"context_{context_id}.json"
            if context_file.exists():
                context_file.unlink()
            
            return True
        except Exception:
            return False
    
    def cleanup_worker_contexts(self) -> int:
        """
        清理当前工作线程的所有上下文
        :return: 清理的上下文数量
        """
        worker_id = self.get_worker_id()
        cleaned_count = 0
        
        try:
            with self._lock:
                # 找到属于当前worker的上下文
                contexts_to_remove = [
                    ctx_id for ctx_id in self._context_storage.keys()
                    if ctx_id.startswith(worker_id)
                ]
                
                # 从内存中移除
                for ctx_id in contexts_to_remove:
                    del self._context_storage[ctx_id]
                    cleaned_count += 1
            
            # 清理文件
            for context_file in self.temp_dir.glob(f"context_{worker_id}_*.json"):
                context_file.unlink()
            
        except Exception:
            pass
        
        return cleaned_count
    
    def _save_context_to_file(self, context: WorkflowContext) -> bool:
        """保存上下文到文件"""
        try:
            context_file = self.temp_dir / f"context_{context.context_id}.json"
            context_data = {
                'context_id': context.context_id,
                'workflow_name': context.workflow_name,
                'case_index': context.case_index,
                'variables': context.variables,
                'step_results': context.step_results,
                'created_time': context.created_time,
                'last_updated': context.last_updated
            }
            
            with open(context_file, 'w', encoding='utf-8') as f:
                json.dump(context_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception:
            return False
    
    def _load_context_from_file(self, context_id: str) -> Optional[WorkflowContext]:
        """从文件加载上下文"""
        try:
            # 首先尝试当前worker目录
            context_file = self.temp_dir / f"context_{context_id}.json"
            
            if not context_file.exists():
                # 如果当前worker目录没有，尝试查找其他worker目录
                for file_path in self.temp_dir.glob(f"**/context_{context_id}.json"):
                    context_file = file_path
                    break
            
            if context_file.exists():
                with open(context_file, 'r', encoding='utf-8') as f:
                    context_data = json.load(f)
                
                context = WorkflowContext(
                    context_id=context_data['context_id'],
                    workflow_name=context_data['workflow_name'],
                    case_index=context_data['case_index'],
                    variables=context_data.get('variables', {}),
                    step_results=context_data.get('step_results', []),
                    created_time=context_data.get('created_time', time.time()),
                    last_updated=context_data.get('last_updated', time.time())
                )
                
                return context
        except Exception:
            pass
        
        return None
