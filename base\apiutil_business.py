# -*- coding: utf-8 -*-
# sys.path.insert(0, "..")


from common.sendrequest import SendRequest
from common.readyaml import ReadYamlData
from common.recordlog import logs
from common.variable_processor import VariableProcessor
from conf.operationConfig import OperationConfig
from common.assertions import Assertions
from common.debugtalk import DebugTalk
import allure
import json
import jsonpath
import re
import traceback
from json.decoder import JSONDecodeError
from conf.env_manager import env_manager
import time
import uuid
import copy

assert_res = Assertions()


class RequestBase(object):
    def __init__(self):
        self.run = SendRequest()
        self.read = ReadYamlData()
        self.conf = OperationConfig()
        self.env_config = env_manager.get_current_env_config()

    def handler_yaml_list(self, data_dict):
        """处理yaml文件测试用例请求参数为list情况，以数组形式"""
        try:
            for key, value in data_dict.items():
                if isinstance(value, list):
                    value_lst = ','.join(value).split(',')
                    data_dict[key] = value_lst
                return data_dict
        except Exception:
            logs.error(str(traceback.format_exc()))

    def replace_load(self, data):
        """yaml数据替换解析"""
        str_data = data
        if not isinstance(data, str):
            str_data = json.dumps(data, ensure_ascii=False)
        for i in range(str_data.count('${')):
            if '${' in str_data and '}' in str_data:
                # index检测字符串是否子字符串，并找到字符串的索引位置
                start_index = str_data.index('$')
                end_index = str_data.index('}', start_index)
                # yaml文件的参数，如：${get_yaml_data(loginname)}
                ref_all_params = str_data[start_index:end_index + 1]
                # 函数名，获取Debugtalk的方法
                func_name = ref_all_params[2:ref_all_params.index("(")]
                # 函数里的参数
                func_params = ref_all_params[ref_all_params.index("(") + 1:ref_all_params.index(")")]
                # 传入替换的参数获取对应的值,*func_params按,分割重新得到一个字符串
                extract_data = getattr(DebugTalk(), func_name)(*func_params.split(',') if func_params else "")
                if extract_data and isinstance(extract_data, list):
                    extract_data = ','.join(e for e in extract_data)
                str_data = str_data.replace(ref_all_params, str(extract_data))
        # 还原数据
        if data and isinstance(data, dict):
            data = json.loads(str_data)
            self.handler_yaml_list(data)
        else:
            data = str_data
        return data

    def specification_yaml(self, base_info, testcase):
        """
        规范yaml测试用例的写法
        :param case_info: dict类型
        :param testcase: dict类型
        :return: res：requests.Response类型
        规范yaml文件的测试用例写法，主要是处理请求参数和断言结果的解析替换
        """

        # params_type = ['params', 'data', 'json']
        cookie = None
        try:
            # 获取环境变量中的host
            case_url = base_info["url"]
            if 'http' not in case_url:
                base_url = self.env_config.get('base_url')
                url = base_url + case_url
            else:
                url = case_url
            api_name = base_info["api_name"]
            method = base_info["method"]
            header = self.replace_load(base_info["header"])
            encrypt_type = base_info.get("encrypt_type", None)
            case_info_text =f'请求URL：{url}\n' \
                            f'接口名称：{api_name}\n'\
                            f'请求方法：{method}\n' \
                            f'请求头：{header}\n' \
                            f'加密类型：{encrypt_type}\n' \
            # 处理请求参数
            allure.attach(case_info_text, '接口请求信息', allure.attachment_type.TEXT)
            try:
                cookie = self.replace_load(base_info["cookies"])
                allure.attach(str(cookie), 'Cookie', allure.attachment_type.TEXT)
            except:
                pass
            tc = testcase
            case_name = tc.pop("case_name")
            allure.attach(case_name, f'测试用例名称：{case_name}', allure.attachment_type.TEXT)
            # 断言结果解析替换
            val = self.replace_load(tc.get('validation'))
            tc['validation'] = val
            validation = eval(tc.pop('validation'))
            allure_validation = str([str(list(i.values())) for i in validation])
            allure.attach(allure_validation, "预期结果", allure.attachment_type.TEXT)
            # 提取参数
            extract = tc.pop('extract', None)
            extract_lst = tc.pop('extract_list', None) # 当前没有这个数据
            # 处理请求参数
            file, files = tc.pop("files", None), None
            if file is not None:
                for fk, fv in file.items():
                    allure.attach(json.dumps(file), '导入文件')
                    files = {fk: open(fv, 'rb')}
            res = self.run.run_main(name=api_name,
                                    url=url,
                                    case_name=case_name,
                                    header=header,
                                    cookies=cookie,
                                    method=method,
                                    encrypt_type=encrypt_type,
                                    file=files, **tc)
            res_text = res.text
            # allure.attach(res_text, '接口响应信息', allure.attachment_type.TEXT)
            status_code = res.status_code
            allure.attach(self.allure_attach_response(res.json()), '接口响应信息', allure.attachment_type.TEXT)

            try:
                res_json = json.loads(res_text)
                if extract is not None:
                    self.extract_data(extract, res_text)
                if extract_lst is not None:
                    self.extract_data_list(extract_lst, res_text)
                # 处理断言
                assert_res.assert_result(validation, res_json, status_code)
            except JSONDecodeError as js:
                logs.error("系统异常或接口未请求！")
                raise js
            except Exception as e:
                logs.error(str(traceback.format_exc()))
                raise e
            return res
        except Exception as e:
            logs.error(e)
            raise e

    @classmethod
    def allure_attach_response(cls, response):
        if isinstance(response, dict):
            allure_response = json.dumps(response, ensure_ascii=False, indent=4)
        else:
            allure_response = response
        return allure_response

    def extract_data(self, testcase_extract, response):
        """
        当前暂无list变量数据的存储
        提取接口的返回参数，支持正则表达式和json提取，提取单个参数
        :param testcase_extract: testcase文件yaml中的extract值
        :param response: 接口的实际返回值,str类型
        :return:
        """
        pattern_lst = ['(.+?)', '(.*?)', r'(\d+)', r'(\d*)']
        try:
            for key, value in testcase_extract.items():
                for pat in pattern_lst:
                    if pat in value:
                        ext_list = re.search(value, response)
                        if pat in [r'(\d+)', r'(\d*)']:
                            extract_date = {key: int(ext_list.group(1))}
                        else:
                            extract_date = {key: ext_list.group(1)}
                        logs.info('正则提取到的参数：%s' % extract_date)
                        self.read.write_yaml_data(extract_date)
                if "$" in value:
                    ext_json = jsonpath.jsonpath(json.loads(response), value)[0]
                    if ext_json:
                        extract_date = {key: ext_json}
                        allure.attach(str(ext_json), f'{key}提取到的参数:{ext_json}', allure.attachment_type.TEXT)
                    else:
                        extract_date = {key: "未提取到数据，该接口返回结果可能为空"}
                    logs.info('json提取到参数：%s' % extract_date)
                    self.read.write_yaml_data(extract_date)
        except:
            logs.error('接口返回值提取异常，请检查yaml文件extract表达式是否正确！')

    def extract_data_list(self, testcase_extract_list, response):
        """
        提取多个参数，支持正则表达式和json提取，提取结果以列表形式返回
        :param testcase_extract_list: yaml文件中的extract_list信息
        :param response: 接口的实际返回值,str类型
        :return:
        """
        try:
            for key, value in testcase_extract_list.items():
                if "(.+?)" in value or "(.*?)" in value:
                    ext_list = re.findall(value, response, re.S)
                    if ext_list:
                        extract_date = {key: ext_list}
                        logs.info('正则提取到的参数：%s' % extract_date)
                        self.read.write_yaml_data(extract_date)
                if "$" in value:
                    # 增加提取判断，有些返回结果为空提取不到，给一个默认值
                    ext_json = jsonpath.jsonpath(json.loads(response), value)
                    if ext_json:
                        extract_date = {key: ext_json}
                        allure.attach(str(ext_json), f'{key}提取到的参数:{ext_json}', allure.attachment_type.TEXT)
                    else:
                        extract_date = {key: "未提取到数据，该接口返回结果可能为空"}
                    logs.info('json提取到参数：%s' % extract_date)
                    self.read.write_yaml_data(extract_date)
        except:
            logs.error('接口返回值提取异常，请检查yaml文件extract_list表达式是否正确！')

    def create_workflow_context(self):
        """
        为工作流创建独立的上下文环境，避免并行测试时变量冲突
        :return: 上下文ID
        """
        context_id = str(uuid.uuid4())[:8]
        return context_id

    def get_context_variable(self, context_id, key):
        """
        从上下文中获取变量
        :param context_id: 上下文ID
        :param key: 变量key
        :return: 变量值
        """
        context_key = f"{context_id}_{key}"
        return self.read.get_extract_yaml(context_key)

    def set_context_variable(self, context_id, data_dict):
        """
        设置上下文变量
        :param context_id: 上下文ID
        :param data_dict: 变量字典
        """
        context_data = {}
        for key, value in data_dict.items():
            context_key = f"{context_id}_{key}"
            context_data[context_key] = value
        self.read.write_yaml_data(context_data)

    def get_template_data_by_pay_type(self, template_path, pay_type=None, template_key=None):
        """
        根据支付类型或模板key获取默认数据模板
        :param template_path: 模板文件路径
        :param pay_type: 支付类型 0-自主赔付 1-微信 2-支付宝 3-银行卡
        :param template_key: 直接指定模板key
        :return: 对应的默认数据
        """
        try:
            template_data = self.read.read_yaml_data(template_path)
            if not isinstance(template_data, dict) or not template_data:
                logs.error(f"模板数据格式错误或为空: {template_data}")
                return {}

            if template_key:
                # 直接使用指定的模板key
                return template_data.get(template_key, {})

            if pay_type is not None:
                # 根据支付类型映射到对应的数据模板
                pay_type_mapping = {
                    '0': 'data_default',      # 自主赔付
                    '1': 'wx_data_default',   # 微信赔付
                    '2': 'zfb_data_default',  # 支付宝赔付
                    '3': 'yhk_data_default'   # 银行卡赔付
                }
                template_key = pay_type_mapping.get(str(pay_type), 'data_default')
                logs.info(f"根据支付类型 {pay_type} 选择数据模板: {template_key}")
                return template_data.get(template_key, {})

            # 默认返回第一个模板
            first_value = next(iter(template_data.values()), {})
            return first_value

        except Exception as e:
            logs.error(f"获取默认数据模板失败: {e}\n{traceback.format_exc()}")
            return {}
    def replace_load_with_context(self, data, context_id):
        """
        支持上下文的变量替换
        :param data: 待替换的数据
        :param context_id: 上下文ID
        :return: 替换后的数据
        """
        if not data:
            return data
            
        try:
            # 确保数据能正确序列化
            if isinstance(data, str):
                str_data = data
            else:
                str_data = json.dumps(data, ensure_ascii=False)
            
            # 先处理上下文变量 {{variable_name}}
            import re
            context_pattern = r'\{\{(\w+)\}\}'
            context_matches = re.findall(context_pattern, str_data)
            
            for var_name in context_matches:
                var_value = self.get_context_variable(context_id, var_name)
                if var_value is not None:
                    str_data = str_data.replace('{{' + var_name + '}}', str(var_value))
                    logs.debug(f"上下文变量替换: {var_name} -> {var_value}")
            
            # 再进行常规的变量替换
            if isinstance(data, str):
                final_data = self.replace_load(str_data)
            else:
                final_data = self.replace_load(json.loads(str_data))
            final_data = VariableProcessor.process_json_data(final_data)
            return final_data
        except Exception as e:
            error_msg = f"上下文变量替换失败: {e}"
            logs.error(error_msg)
            logs.error(f"原始数据: {data}")
            logs.error(f"上下文ID: {context_id}")
            allure.attach(error_msg, '变量替换错误', allure.attachment_type.TEXT)
            return data

    def specification_yaml_with_context(self, base_info, testcase, context_id):
        """
        支持上下文的YAML测试用例执行
        :param base_info: 基础信息
        :param testcase: 测试用例
        :param context_id: 上下文ID
        :return: 响应结果
        """
        try:
            # 执行原有的specification_yaml逻辑，但在提取数据时使用上下文
            tc = copy.deepcopy(testcase)
            extract = tc.pop('extract', None)
            
            logs.info(f"执行接口调用 - 上下文ID: {context_id}")
            logs.debug(f"API配置: {base_info}")
            logs.debug(f"测试用例: {tc}")
            
            # 执行接口调用
            result = self.specification_yaml(base_info, tc)
            
            # 如果有提取配置，使用上下文存储
            if extract and result:
                try:
                    response_text = result.text
                    extract_data = {}
                    
                    logs.info(f"开始提取上下文变量: {extract}")
                    
                    for key, value in extract.items():
                        if "$" in value:
                            try:
                                response_json = json.loads(response_text)
                                ext_json = jsonpath.jsonpath(response_json, value)
                                if ext_json:
                                    extract_data[key] = ext_json[0]
                                    logs.info(f"成功提取变量: {key} = {ext_json[0]}")
                                    allure.attach(str(ext_json[0]), f'{key}提取到的参数:{ext_json[0]}', allure.attachment_type.TEXT)
                                else:
                                    extract_data[key] = "未提取到数据，该接口返回结果可能为空"
                                    logs.warning(f"未能提取变量: {key}, 表达式: {value}")
                            except json.JSONDecodeError as jde:
                                logs.error(f"JSON解析失败: {jde}")
                                logs.error(f"响应内容: {response_text}")
                            except Exception as ee:
                                logs.error(f"提取变量 {key} 时发生错误: {ee}")
                    
                    # 存储到上下文中
                    if extract_data:
                        self.set_context_variable(context_id, extract_data)
                        logs.info(f'上下文{context_id}提取到参数：{extract_data}')
                        
                except Exception as e:
                    error_msg = f'上下文变量提取异常：{e}'
                    logs.error(error_msg)
                    logs.error(f"错误详情: {traceback.format_exc()}")
                    allure.attach(error_msg, '变量提取错误', allure.attachment_type.TEXT)
            
            return result
        except Exception as e:
            error_msg = f"执行带上下文的YAML测试用例失败: {e}"
            logs.error(error_msg)
            logs.error(f"错误详情: {traceback.format_exc()}")
            allure.attach(error_msg, 'YAML执行错误', allure.attachment_type.TEXT)
            allure.attach(traceback.format_exc(), '错误堆栈', allure.attachment_type.TEXT)
            raise e
