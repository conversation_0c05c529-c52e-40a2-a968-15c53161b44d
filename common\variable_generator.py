import uuid
import string
import random
import datetime
from typing import Optional
from nanoid import generate

class VariableGenerator:
    @staticmethod
    def generate_uuid() -> str:
        """生成UUID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_alphanumeric(length: int = 10) -> str:
        """生成指定长度的字母数字组合"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    @staticmethod
    def generate_nanoid(size: int = 21) -> str:
        """生成nanoid"""
        return generate(size=size)
    
    @classmethod
    def get_datetime(cls, operation: Optional[str] = None, value: int = 0) -> str:
        """
        获取日期时间，支持基本运算
        :param operation: 操作类型，支持 addMinutes, addHours, addDays
        :param value: 要增加的值
        :return: 格式化的日期时间字符串
        """
        now = datetime.datetime.now()
        
        if operation == 'addMinutes':
            now += datetime.timedelta(minutes=value)
        elif operation == 'addHours':
            now += datetime.timedelta(hours=value)
        elif operation == 'addDays':
            now += datetime.timedelta(days=value)
            
        return now.strftime('%Y-%m-%d %H:%M:%S')

    @classmethod
    def process_variable(cls, variable: str) -> str:
        """
        处理变量标记，生成对应的值
        :param variable: 变量标记，如 $string.uuid, $string.alphanumeric(length=15)
        :return: 生成的值
        """
        if not variable.startswith('$'):
            return variable
            
        parts = variable.lstrip('$').split('.')
        if len(parts) != 2:
            return variable
            
        category, func = parts
        
        if category == 'string':
            if func == 'uuid':
                return cls.generate_uuid()
            elif func == 'nanoid':
                return cls.generate_nanoid()
            elif func.startswith('alphanumeric'):
                # 解析length参数
                length = 10  # 默认长度
                if '(' in func:
                    try:
                        length = int(func.split('length=')[1].rstrip(')'))
                    except (IndexError, ValueError):
                        pass
                return cls.generate_alphanumeric(length)
                
        elif category == 'date':
            if func == 'now':
                return cls.get_datetime()
            elif '|' in func:
                base, operation = func.split('|')
                if base != 'now':
                    return variable
                    
                if '(' in operation and ')' in operation:
                    op_name = operation.split('(')[0]
                    try:
                        value = int(operation.split('(')[1].rstrip(')'))
                    except (IndexError, ValueError):
                        value = 0
                    return cls.get_datetime(op_name, value)
        elif category == 'phone':
            if func == 'mobile':
                # 生成随机手机号，假设中国手机号格式
                return f'1{random.randint(3, 9)}{"".join(random.choices(string.digits, k=9))}'
        return variable 