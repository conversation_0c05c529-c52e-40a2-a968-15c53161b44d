import os
import yaml
from typing import Dict, Any, Optional
from conf.env_manager import env_manager

class EnvVariables:
    def __init__(self):
        self._variables = {}
        self._load_env_config()
        self._load_custom_variables()
    
    def _load_env_config(self):
        """加载环境配置"""
        env_config = env_manager.get_current_env_config()
        if env_config:
            self._variables.update({
                'partnerId': env_config.get('partner_id', ''),
                'key': env_config.get('key', ''),
                'baseUrl': env_config.get('base_url', ''),
                'returnUrl': env_config.get('return_url', ''),
            })
    
    def _load_custom_variables(self):
        """加载自定义变量配置"""
        config_path = os.path.join('conf', 'variables.yaml')
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    custom_vars = yaml.safe_load(f) or {}
                    # 获取当前环境的变量
                    env_name = env_manager.current_env
                    env_vars = custom_vars.get(env_name, {})
                    self._variables.update(env_vars)
            except Exception as e:
                print(f"加载自定义变量配置失败: {str(e)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取环境变量值"""
        return self._variables.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置环境变量值"""
        self._variables[key] = value
    
    def process_variable(self, variable: str) -> str:
        """
        处理环境变量标记
        :param variable: 变量标记，如 {{partnerId}}
        :return: 环境变量的值
        """
        if not (variable.startswith('{{') and variable.endswith('}}') and not variable.startswith('{{$')):
            return variable
            
        var_name = variable[2:-2]
        return str(self.get(var_name, variable))

# 创建全局实例
env_variables = EnvVariables() 