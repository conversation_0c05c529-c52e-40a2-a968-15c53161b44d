import os
import shutil
import time
import psutil
from pathlib import Path
from common.recordlog import logs
import uuid
import subprocess


def safe_remove_file(file_path, max_retries=3, retry_delay=1):
    """
    安全删除文件，支持重试机制
    :param file_path: 文件路径
    :param max_retries: 最大重试次数
    :param retry_delay: 重试间隔（秒）
    """
    for attempt in range(max_retries):
        try:
            if os.path.exists(file_path):
                # 检查文件是否被占用
                if is_file_in_use(file_path):
                    if attempt < max_retries - 1:
                        logs.warning(f"文件被占用，等待 {retry_delay} 秒后重试: {file_path}")
                        time.sleep(retry_delay)
                        continue
                    else:
                        logs.warning(f"文件始终被占用，跳过删除: {file_path}")
                        return False
                
                # 尝试删除文件
                os.chmod(file_path, 0o777)  # 修改权限
                os.remove(file_path)
                logs.info(f"成功删除文件: {file_path}")
                return True
        except PermissionError as e:
            if attempt < max_retries - 1:
                logs.warning(f"删除文件权限不足，重试 {attempt + 1}/{max_retries}: {file_path}")
                time.sleep(retry_delay)
            else:
                logs.error(f"删除文件失败，权限不足: {file_path}, 错误: {e}")
        except Exception as e:
            logs.error(f"删除文件时发生未知错误: {file_path}, 错误: {e}")
            break
    
    return False

def is_file_in_use(file_path):
    """
    检查文件是否被其他进程占用
    """
    try:
        # 方法1：尝试以独占模式打开文件
        with open(file_path, 'r+b') as f:
            pass
        return False
    except (IOError, OSError):
        # 方法2：使用psutil检查进程
        try:
            for proc in psutil.process_iter(['pid', 'open_files']):
                try:
                    if proc.info['open_files']:
                        for file_info in proc.info['open_files']:
                            if os.path.samefile(file_info.path, file_path):
                                logs.warning(f"文件被进程 {proc.info['pid']} 占用: {file_path}")
                                return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess, OSError):
                    continue
        except Exception:
            pass
        return True

def safe_remove_directory(dir_path, max_retries=3, retry_delay=1):
    """
    安全删除目录
    """
    if not os.path.exists(dir_path):
        return True
        
    for attempt in range(max_retries):
        try:
            # 首先尝试删除目录中的所有文件
            for root, dirs, files in os.walk(dir_path, topdown=False):
                for file in files:
                    file_path = os.path.join(root, file)
                    safe_remove_file(file_path, max_retries=1)
                
                for dir in dirs:
                    dir_path_sub = os.path.join(root, dir)
                    try:
                        os.rmdir(dir_path_sub)
                    except OSError:
                        pass
            
            # 删除根目录
            shutil.rmtree(dir_path, ignore_errors=True)
            
            if not os.path.exists(dir_path):
                logs.info(f"成功删除目录: {dir_path}")
                return True
                
        except Exception as e:
            if attempt < max_retries - 1:
                logs.warning(f"删除目录失败，重试 {attempt + 1}/{max_retries}: {dir_path}")
                time.sleep(retry_delay)
            else:
                logs.error(f"删除目录失败: {dir_path}, 错误: {e}")
    
    return False

def create_unique_report_dir():
    """为每个测试运行创建唯一的报告目录"""
    timestamp = int(time.time())
    unique_id = str(uuid.uuid4())[:8]
    report_dir = Path('./report') / f"temp_{timestamp}_{unique_id}"
    report_dir.mkdir(parents=True, exist_ok=True)
    return str(report_dir.absolute())

def cleanup_old_reports(keep_count=5):
    """清理旧的报告目录，保留最新的几个"""
    try:
        report_base = Path('./report')
        if not report_base.exists():
            return
            
        # 获取所有temp_开头的目录
        temp_dirs = [d for d in report_base.iterdir() 
                    if d.is_dir() and d.name.startswith('temp_')]
        
        # 按修改时间排序，保留最新的
        temp_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除多余的目录
        for old_dir in temp_dirs[keep_count:]:
            try:
                safe_remove_directory(str(old_dir))
                logs.info(f"清理旧报告目录: {old_dir}")
            except Exception as e:
                logs.warning(f"清理旧报告目录失败: {old_dir}, 错误: {e}")
                
    except Exception as e:
        logs.error(f"清理旧报告时发生错误: {e}")



def check_allure_installed():
    """检查allure命令是否可用"""
    try:
        # 使用shell=True来确保在Windows系统上也能正确执行命令
        result = subprocess.run('allure --version', shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"检测到Allure版本: {result.stdout.strip()}")
            return True
        print(f"\n错误: allure命令执行失败: {result.stderr}")
        return False
    except Exception as e:
        print(f"\n错误: 检查allure命令时发生异常: {str(e)}")
        print("安装说明:")
        print("1. 下载allure: https://github.com/allure-framework/allure2/releases")
        print("2. 解压到指定目录")
        print("3. 将bin目录添加到系统PATH环境变量")
        print("4. 重启终端并验证安装：allure --version")
        return False

def run_allure_serve(report_dir,is_report=True):
    """运行allure serve命令"""
    try:
        if is_report:
            # 使用shell=True和完整的命令字符串，添加语言参数
            cmd = f'allure serve "{os.path.abspath(report_dir)}"'
            print("\n启动allure报告服务器，按Ctrl+C可以退出...")
            subprocess.run(cmd, shell=True, check=True)
            return True
        else:
            return False
    except KeyboardInterrupt:
        print("\n已停止allure报告服务")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n启动allure报告服务失败: {str(e)}")
        print("请确认allure命令是否正确安装并可用")
        return False