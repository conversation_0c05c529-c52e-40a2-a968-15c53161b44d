#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.getcwd())

from common.readyaml import ReadYamlData

def test_generic_interface_logic():
    """测试通用接口的识别和跳过逻辑"""
    print("=== 测试通用接口识别和跳过逻辑 ===")
    
    yfd = ReadYamlData()
    
    # 获取工作流数据
    workflow_data = yfd.get_workflow_yaml("testcase/DeliveryService/insureInvoce copy.yml")
    
    if not workflow_data:
        print("未能读取到工作流数据")
        return
    
    print(f"共读取到 {len(workflow_data)} 个测试用例组")
    
    for case_group_index, case_group in enumerate(workflow_data):
        print(f"\n--- 测试用例组 {case_group_index + 1} ---")
        
        for step_index, (workflow_info, base_info, testcase) in enumerate(case_group):
            step_name = testcase['case_name']
            skip_execution = testcase['skip_execution']
            forwardsign = testcase.get('forwardsign')
            is_generic = testcase['is_generic_step']
            
            print(f"步骤 {step_index}: {step_name}")
            print(f"  - 是否通用接口: {is_generic}")
            print(f"  - 跳过执行: {skip_execution}")
            print(f"  - 依赖标识: {forwardsign}")
            
            # 特别关注投保状态查询步骤
            if "投保状态查询" in step_name:
                print(f"  *** 这是通用接口步骤，应该根据上一步决定跳过状态 ***")

if __name__ == "__main__":
    test_generic_interface_logic()
