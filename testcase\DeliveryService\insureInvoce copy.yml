workflow_testcases:
  - workflow_name: "投保-理赔-多数据组测试"
    description: "完整的投保到理赔流程测试-支持多数据组"
    steps:
      - step_name: "投保"
        api_config:
          api_name: "投保接口"
          url: "/api/scene/insure"
          method: "post"
          header:
            Content-Type: "application/x-www-form-urlencoded"
          encrypt_type: "TC_Base64"
        data_default: "./testcase/default_yaml/deliver/insure.yml"
        data:
          - caseName: "投保-航综1.0-返航-微信-失败"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          - caseName: "投保-航综3.0-返航-微信-失败重推"
            skip_execution: true
        validation:
          - contains:
              status_code: 200
          - eq:
              $.respCode: "0"
        extract:
          tradeSerialNo: "$.insureData.tradeSerialNo"
          serialNo: "$.insureData.serialNo"
          
      - step_name: "投保状态查询"
        api_config:
          api_name: "投保查询接口"
          url: "/api/scene/query"
          method: "post"
          header:
            Content-Type: "application/x-www-form-urlencoded"
          encrypt_type: "TC_Base64"
        data:
          tradeSerialNo: "{{tradeSerialNo}}"
          serialNo: "{{serialNo}}"
        validation:
          - contains:
              status_code: 200
          - eq:
              $.respCode: '0'
          - eq:
              $.queryData.message: '投保成功'
        extract:
          policyNo: "$.queryData.policyNo"
          sceneCode: "$.queryData.sceneCode"
        max_retry: 3
        retry_interval: 2
        
      - step_name: "申请理赔"
        api_config:
          api_name: "申请理赔接口"
          url: "/api/autoclaim/applyclaim"
          method: "post"
          header:
            Content-Type: "application/x-www-form-urlencoded"
          encrypt_type: "TC_Base64"
        data:
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "700"
            selfClaimedAmount: "700"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - Forwardsign: "case:0,step:2"
            autoClaimBizType: "5"
            selfClaimedSettleAmount: "500"
            selfClaimedAmount: "500"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
        validation:
          - contains:
              status_code: 200
          - eq:
              $.respCode: '0'

