# -*- coding: utf-8 -*-
"""
简化的测试类示例
展示如何用最少的代码替换原有的 test_insurance_claim_workflow 函数
"""

import pytest
import allure
from services.insurance_workflow.core.workflow_service import execute_insurance_claim_workflow


@allure.epic('保险项目')
@allure.feature('投递服务-工作流测试')
@allure.parent_suite('保险项目')
@allure.suite('投递服务-工作流测试')
@allure.sub_suite('投保理赔完整流程')
class TestSimpleInsuranceWorkflow:
    """简化的保险工作流测试类"""
    
    @pytest.mark.run(order=1)
    @allure.story('投保理赔完整流程测试')
    @pytest.mark.smoke
    def test_insurance_claim_workflow_simple(self):
        """
        简化版的投保理赔完整流程测试
        使用封装的工作流服务，代码量大幅减少
        """
        # 执行工作流 - 只需要一行代码！
        results = execute_insurance_claim_workflow(
            'testcase/DeliveryService/insureInvoce copy.yml'
        )
        
        # 验证执行结果
        assert len(results) > 0, "工作流用例数据为空，跳过测试"
        
        for result in results:
            # 设置Allure标题（已在服务内部处理，这里可选）
            allure.dynamic.title(result.workflow_name)
            
            # 验证工作流执行成功
            assert result.success, f"工作流 {result.workflow_name} 执行失败: {result.error_message}"
            
            # 验证所有步骤都执行成功
            for step_result in result.step_results:
                if not step_result.success:
                    pytest.fail(f"步骤执行失败: {step_result.error}")
            
            # 记录执行信息
            print(f"✅ 工作流 {result.workflow_name} 执行成功")
            print(f"   执行时间: {result.execution_time:.2f}秒")
            print(f"   步骤数量: {len(result.step_results)}")


@allure.epic('保险项目')
@allure.feature('投递服务-工作流测试')
@pytest.mark.test_work
class TestInsuranceWorkflowWithService:
    """使用服务实例的保险工作流测试类"""
    
    def setup_class(self):
        """类级别的setup"""
        from services.insurance_workflow.core.workflow_service import create_workflow_service
        self.workflow_service = create_workflow_service()
    
    def teardown_class(self):
        """类级别的teardown"""
        self.workflow_service.cleanup_all_contexts()
    
    @pytest.mark.parametrize('yaml_file', [
        'testcase/DeliveryService/insureInvoce copy.yml',
        # 可以添加更多的YAML文件
    ])
    def test_insurance_workflow_parameterized(self, yaml_file):
        """
        参数化的工作流测试
        可以轻松测试多个不同的工作流配置文件
        """
        # 执行工作流
        results = self.workflow_service.execute_workflow_from_yaml(yaml_file)
        
        # 验证结果
        assert len(results) > 0, f"工作流文件 {yaml_file} 没有生成测试用例"
        
        for result in results:
            assert result.success, f"工作流 {result.workflow_name} 执行失败: {result.error_message}"
            
            # 可以获取执行过程中的变量
            variables = self.workflow_service.get_context_variables(result.context_id)
            print(f"工作流变量: {variables}")
    
    def test_single_insure_step(self):
        """
        单独测试投保步骤
        展示如何测试工作流中的单个步骤
        """
        step_config = {
            'step_name': '投保接口测试',
            'api_config': {
                'api_name': '投保接口',
                'url': '/api/scene/insure',
                'method': 'post',
                'header': {'Content-Type': 'application/x-www-form-urlencoded'},
                'encrypt_type': 'TC_Base64'
            },
            'data': {
                'planCode': '00200005003',
                'effectTime': '{{$date.now|addMinutes(-1)}}',
                'expiryTime': '{{$date.now|addDays(1)}}',
                'totalPremium': '50',
                'sumAssured': '62000990'
            },
            'extract': {
                'tradeSerialNo': '$.insureData.tradeSerialNo',
                'serialNo': '$.insureData.serialNo'
            }
        }
        
        # 执行单个步骤
        step_result, context_id = self.workflow_service.execute_single_step(step_config)
        
        # 验证结果
        assert step_result.success, f"投保步骤执行失败: {step_result.error}"
        
        # 验证提取的变量
        variables = self.workflow_service.get_context_variables(context_id)
        assert 'tradeSerialNo' in variables, "应该提取到交易流水号"
        assert 'serialNo' in variables, "应该提取到序列号"


# 兼容原有测试方法的包装函数
def test_insurance_claim_workflow_compatible():
    """
    兼容原有测试方法的包装函数
    可以直接替换原有的 test_insurance_claim_workflow 函数
    """
    # 这个函数可以直接替换原有的复杂实现
    results = execute_insurance_claim_workflow(
        'testcase/DeliveryService/insureInvoce copy.yml'
    )
    
    # 简单的验证
    for result in results:
        if not result.success:
            raise AssertionError(f"工作流执行失败: {result.error_message}")
    
    return results


# 如果需要在其他测试类中使用，可以创建一个基类
class BaseInsuranceWorkflowTest:
    """保险工作流测试基类"""
    
    def setup_class(self):
        """初始化工作流服务"""
        from services.insurance_workflow.core.workflow_service import create_workflow_service
        self.workflow_service = create_workflow_service()
    
    def teardown_class(self):
        """清理资源"""
        self.workflow_service.cleanup_all_contexts()
    
    def execute_workflow(self, yaml_file: str):
        """执行工作流的通用方法"""
        results = self.workflow_service.execute_workflow_from_yaml(yaml_file)
        
        for result in results:
            assert result.success, f"工作流 {result.workflow_name} 执行失败: {result.error_message}"
        
        return results
    
    def execute_step(self, step_config: dict):
        """执行单个步骤的通用方法"""
        step_result, context_id = self.workflow_service.execute_single_step(step_config)
        assert step_result.success, f"步骤执行失败: {step_result.error}"
        return step_result, context_id


# 继承基类的具体测试类示例
class TestMyInsuranceWorkflow(BaseInsuranceWorkflowTest):
    """继承基类的具体测试类"""
    
    def test_my_workflow(self):
        """我的工作流测试"""
        results = self.execute_workflow('testcase/DeliveryService/insureInvoce copy.yml')
        
        # 可以添加特定的验证逻辑
        for result in results:
            print(f"执行了工作流: {result.workflow_name}")


if __name__ == "__main__":
    # 直接运行测试
    test_insurance_claim_workflow_compatible()
