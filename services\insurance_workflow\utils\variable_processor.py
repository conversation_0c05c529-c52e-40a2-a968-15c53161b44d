# -*- coding: utf-8 -*-
"""
变量处理器
提供变量替换和数据处理功能
"""

import re
import os
import random
import string
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Union
from services.insurance_workflow.utils.logger import get_workflow_logger

# 可选依赖，如果不存在则使用默认实现
try:
    from conf.env_manager import env_manager
    ENV_MANAGER_AVAILABLE = True
except ImportError:
    ENV_MANAGER_AVAILABLE = False

try:
    from nanoid import generate
    NANOID_AVAILABLE = True
except ImportError:
    NANOID_AVAILABLE = False

class VariableGenerator:
    """变量生成器"""
    
    @staticmethod
    def process_variable(var_expression: str) -> str:
        """
        处理变量表达式
        :param var_expression: 变量表达式，如 $date.now|addDays(1)
        :return: 处理后的值
        """
        try:
            # 解析变量表达式
            if var_expression.startswith('$date.'):
                return VariableGenerator._process_date_variable(var_expression)
            elif var_expression.startswith('$string.'):
                return VariableGenerator._process_string_variable(var_expression)
            elif var_expression.startswith('$number.'):
                return VariableGenerator._process_number_variable(var_expression)
            elif var_expression.startswith('$phone.'):
                return VariableGenerator._process_phone_variable(var_expression)
            else:
                return var_expression
        except Exception:
            return var_expression
    
    @staticmethod
    def _process_date_variable(var_expression: str) -> str:
        """处理日期变量"""
        # 解析日期表达式，如 $date.now|addDays(1)|format(yyyy-MM-dd)
        parts = var_expression.split('|')
        base_expr = parts[0]
        
        # 获取基础日期
        if base_expr == '$date.now':
            current_date = datetime.now()
        else:
            current_date = datetime.now()
        
        # 处理修饰符
        for modifier in parts[1:]:
            if modifier.startswith('addDays('):
                days = int(modifier[8:-1])
                current_date += timedelta(days=days)
            elif modifier.startswith('addHours('):
                hours = int(modifier[9:-1])
                current_date += timedelta(hours=hours)
            elif modifier.startswith('addMinutes('):
                minutes = int(modifier[11:-1])
                current_date += timedelta(minutes=minutes)
            elif modifier.startswith('format('):
                format_str = modifier[7:-1]
                # 简单的格式转换
                if format_str == 'yyyy-MM-dd':
                    return current_date.strftime('%Y-%m-%d')
                elif format_str == 'yyyy-MM-dd HH:mm:ss':
                    return current_date.strftime('%Y-%m-%d %H:%M:%S')
        
        # 默认格式
        return current_date.strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def _process_string_variable(var_expression: str) -> str:
        """处理字符串变量"""
        if var_expression.startswith('$string.alphanumeric('):
            # 提取长度参数
            length_match = re.search(r'length=(\d+)', var_expression)
            length = int(length_match.group(1)) if length_match else 10
            
            # 生成字母数字字符串
            chars = string.ascii_letters + string.digits
            return ''.join(random.choice(chars) for _ in range(length))
        
        elif var_expression.startswith('$string.numeric('):
            # 提取长度参数
            length_match = re.search(r'length=(\d+)', var_expression)
            length = int(length_match.group(1)) if length_match else 6
            
            # 生成数字字符串
            return ''.join(random.choice(string.digits) for _ in range(length))
        # uuid
        elif var_expression == '$string.uuid':
            return str(uuid.uuid4())
        # nanoid
        elif var_expression == '$string.nanoid':
            if NANOID_AVAILABLE:
                return generate(size=21)
            else:
                # 如果nanoid不可用，使用uuid作为替代
                return str(uuid.uuid4()).replace('-', '')[:21]
        return var_expression
    
    @staticmethod
    def _process_number_variable(var_expression: str) -> str:
        """处理数字变量"""
        if var_expression == '$number.random':
            return str(random.randint(1000, 9999))
        return var_expression
    
    @staticmethod
    def _process_phone_variable(var_expression: str) -> str:
        """处理手机号变量"""
        if var_expression == '$phone.mobile':
            # 生成模拟手机号
            prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                       '150', '151', '152', '153', '155', '156', '157', '158', '159',
                       '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
            prefix = random.choice(prefixes)
            suffix = ''.join(random.choice(string.digits) for _ in range(8))
            return prefix + suffix
        return var_expression


class WorkflowVariableProcessor:
    """工作流变量处理器"""
    
    def __init__(self):
        self.logger = get_workflow_logger()
        self.env_variables = self._load_env_variables()
    
    def _load_env_variables(self) -> Dict[str, Any]:
        """加载环境变量"""
        env_variables = {}

        if ENV_MANAGER_AVAILABLE:
            try:
                env_config = env_manager.get_current_env_config()
                if env_config:
                    env_variables.update({
                        'partnerId': env_config.get('partner_id', ''),
                        'key': env_config.get('key', ''),
                        'baseUrl': env_config.get('base_url', ''),
                        'returnUrl': env_config.get('return_url', ''),
                    })
            except Exception:
                # 如果环境管理器出错，使用默认值
                pass

        # 如果环境管理器不可用或没有配置，使用默认值
        if not env_variables:
            env_variables = {
                'partnerId': os.getenv('PARTNER_ID', 'TEST_PARTNER'),
                'returnUrl': os.getenv('RETURN_URL', 'http://test.callback.url'),
                'key': os.getenv('KEY', ''),
                'baseUrl': os.getenv('BASE_URL', ''),
            }

        return env_variables
    
    def _is_variable(self, value: str) -> bool:
        """判断是否为变量表达式"""
        return isinstance(value, str) and value.startswith('{{') and value.endswith('}}')
    
    def _process_single_value(self, value: str) -> str:
        """处理单个变量值"""
        if not self._is_variable(value):
            return value
        
        # 处理随机变量 {{$xxx}}
        if value.startswith('{{$'):
            return VariableGenerator.process_variable(value[2:-2])
        
        # 处理环境变量 {{xxx}}
        var_name = value[2:-2]
        env_value = self.env_variables.get(var_name)
        if env_value is not None:
            return str(env_value)
        
        return value
    
    def process_value(self, value: Any) -> Any:
        """递归处理值中的变量"""
        if isinstance(value, str):
            return self._process_single_value(value)
        elif isinstance(value, dict):
            return {k: self.process_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self.process_value(item) for item in value]
        return value
    
    def merge_json_data(self, base_json: Dict[str, Any], case_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并基础JSON数据和用例JSON数据
        :param base_json: 基础JSON数据
        :param case_json: 用例JSON数据
        :return: 合并后的JSON数据
        """
        if not base_json:
            return case_json or {}
        if not case_json:
            return base_json.copy()
        
        # 深拷贝基础数据
        result = self._deep_copy_dict(base_json)
        
        # 递归合并用例数据
        self._merge_dict_recursive(result, case_json)
        
        return result
    
    def _deep_copy_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """深拷贝字典"""
        result = {}
        for key, value in data.items():
            if isinstance(value, dict):
                result[key] = self._deep_copy_dict(value)
            elif isinstance(value, list):
                result[key] = self._deep_copy_list(value)
            else:
                result[key] = value
        return result
    
    def _deep_copy_list(self, data: List[Any]) -> List[Any]:
        """深拷贝列表"""
        result = []
        for item in data:
            if isinstance(item, dict):
                result.append(self._deep_copy_dict(item))
            elif isinstance(item, list):
                result.append(self._deep_copy_list(item))
            else:
                result.append(item)
        return result
    
    def _merge_dict_recursive(self, base_dict: Dict[str, Any], case_dict: Dict[str, Any]):
        """递归合并字典"""
        for key, value in case_dict.items():
            if key in base_dict:
                if isinstance(base_dict[key], dict) and isinstance(value, dict):
                    # 递归合并字典
                    self._merge_dict_recursive(base_dict[key], value)
                elif isinstance(base_dict[key], list) and isinstance(value, list):
                    # 合并列表
                    base_dict[key] = self._merge_lists(base_dict[key], value)
                else:
                    # 直接覆盖
                    base_dict[key] = value
            else:
                # 新增键值对
                base_dict[key] = value
    
    def _merge_lists(self, base_list: List[Any], case_list: List[Any]) -> List[Any]:
        """合并列表"""
        result = base_list.copy()
        
        # 如果case_list更长，用case_list的元素覆盖或扩展
        for i, item in enumerate(case_list):
            if i < len(result):
                if isinstance(result[i], dict) and isinstance(item, dict):
                    # 合并字典元素
                    self._merge_dict_recursive(result[i], item)
                else:
                    # 直接覆盖
                    result[i] = item
            else:
                # 扩展列表
                result.append(item)
        
        return result
    
    def process_json_data(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理JSON数据中的所有变量
        :param json_data: 原始JSON数据
        :return: 处理后的JSON数据
        """
        return self.process_value(json_data)
    
    def set_env_variable(self, key: str, value: Any):
        """设置环境变量"""
        self.env_variables[key] = value
    
    def get_env_variable(self, key: str, default: Any = None) -> Any:
        """获取环境变量"""
        return self.env_variables.get(key, default)


# 兼容性函数，保持与原有代码的兼容
class VariableProcessor:
    """兼容原有VariableProcessor的静态类"""

    _processor = WorkflowVariableProcessor()

    @classmethod
    def process_json_data(cls, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理JSON数据中的所有变量"""
        return cls._processor.process_json_data(json_data)

    @classmethod
    def merge_json_data(cls, base_json: Dict[str, Any], case_json: Dict[str, Any]) -> Dict[str, Any]:
        """合并基础JSON数据和用例JSON数据"""
        return cls._processor.merge_json_data(base_json, case_json)

    @classmethod
    def process_value(cls, value: Any) -> Any:
        """递归处理值中的变量"""
        return cls._processor.process_value(value)
