[MYSQL]
host = *****
port = 3306
username = root
password = 12345
database = zqsjfx

[REDIS]
host = Redis服务器ip
port = 7005
username = ""
password = ""
db = db0

[CLICKHOUSE]
host = *******
port = 8123
username = default
password =
timeout = 10
db = default

[MongoDB]
host = MongoDB库的ip地址
port = 27017
username = admin
password = 123456
database = admin

[EMAIL]
host = smtp.163.com
port = 25
user = 发件人邮箱地址
passwd = *****这里填邮箱的授权码，不是邮箱登录密码*****
addressee = 收件邮箱地址，多个收件人以;隔开，但是这里不用这个邮件发送，使用的是jenkins自带的邮件发送功能
subject = 接口测试

[SSH]
host = ******
port = 22
username = root
password = *******
timeout = 10
command = cat /logs/web_app/zjjtsjzf/zjjtsjzf-api/common-default.log

;默认为allure，则生成allure报告，若=tm，则生成tmreport报告
[REPORT_TYPE]
type = allure

[API_TIMEOUT]
timeout = 10