import json
import base64
import hashlib
from typing import Dict, Union, Any

class RequestEncryption:
    @staticmethod
    def normalize_json(data: Union[str, Dict[str, Any]]) -> str:
        """
        标准化JSON数据
        :param data: JSON字符串或字典
        :return: 标准化后的JSON字符串
        """
        if isinstance(data, str):
            # 如果输入是字符串，先解析成字典
            data = json.loads(data)
        # 将字典重新序列化为标准化的JSON字符串
        return json.dumps(data, separators=(',', ':'), sort_keys=True)

    @staticmethod
    def base64_encode(data: str) -> str:
        """
        对数据进行Base64编码
        :param data: 要编码的字符串
        :return: Base64编码后的字符串
        """
        # 将字符串转换为字节，进行base64编码，然后转回字符串
        return base64.b64encode(data.encode('utf-8')).decode('utf-8')

    @staticmethod
    def calculate_md5(data: str) -> str:
        """
        计算字符串的MD5值
        :param data: 要计算MD5的字符串
        :return: MD5哈希值（小写）
        """
        return hashlib.md5(data.encode('utf-8')).hexdigest().lower()

    @classmethod
    def encrypt_request_TC_Base64(cls, json_data: Union[str, Dict[str, Any]], key: str, partner_id: str) -> Dict[str, str]:
        """
        加密请求数据
        :param json_data: 原始JSON数据（字符串或字典）
        :param key: 加密密钥
        :param partner_id: 合作伙伴ID
        :return: 包含加密后数据的字典
        """
        try:
            # 1. 标准化JSON数据
            normalized_json = cls.normalize_json(json_data)
            
            # 2. Base64编码
            base64_data = cls.base64_encode(normalized_json)
            
            # 3. 拼接Base64数据和密钥
            sign_data = base64_data + key
            
            # 4. 计算MD5签名
            signature = cls.calculate_md5(sign_data)
            
            # 5. 返回最终的请求数据
            return {
                'data': base64_data,
                'partnerId': partner_id,
                'sign': signature
            }
            
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON数据格式错误: {str(e)}")
        except Exception as e:
            raise RuntimeError(f"加密过程出错: {str(e)}")

# 使用示例
# if __name__ == '__main__':
#     # 测试数据
#     test_data = {"serialNo":"03b60860-f5c6-4f19-98dd-675d91409961","partnerId":"TEST1","orderNo":"R5EkxiMVuqEoTxG","orderAmount":"1005.68","planCode":"***********","applyType":"D","applyPeriod":"1","effectTime":"2025-05-16 14:31:38","expiryTime":"2025-05-17 14:31:38","requestTime":"2025-05-16 14:31:38","totalPremium":"2","sumAssured":"1000.00","returnUrl":"https://back.n.nkomol.cn/callback","memberId":"cD1ygt6NLDXJs4dY394ob","insuranceType":"2","separateAccount":"1","insuranceNo":"","rebookOrderNo":"","paySerialNo":"","payChannel":"","traceStatus":"1","traceCode":"","holderInfo":{"holderType":"1","holderName":"产品测试","holderGender":"M","holderBirthday":"1944-10-19","holderCardType":"1","identityStartDate":"2015-10-24","identityEndDate":"2065-10-25","holderCardNo":"530102194410193109","holderMobile":"***********","holderEmail":"<EMAIL>","holderIncome":"5.56","occupationCode":"1223","countryCode":"CN","provinceCode":"440000","cityCode":"440100","districtCode":"440106","streetCode":"*********","address":"天河区珠江新城华夏路10号","holderZip":"510623","incomeSource":"工资收入","marriage":"1","unitOfNature":"0"},"insuredInfos":[{"insuredName":"产品测试","insuredGender":"M","insuredBirthday":"1944-10-19","insuredCardType":"1","insuredCardNo":"530102194410193109","identityStartDate":"2014-10-24","identityEndDate":"2065-10-24","insuredMobile":"***********","insuredEmail":"<EMAIL>","insuredRelation":"1","isSocialSecurity":"0","height":"175","weight":"120","marriage":"0","insuredIncome":"5.58","insuredNum":"1","occupationCode":"2335","countryCode":"CN","provinceCode":"440000","cityCode":"440100","districtCode":"*********","streetCode":"NYC001-A","address":"天河区珠江新城华夏路10号","insuredZip":"510623","insuredEname":"surename","benefitType":"1"},{"insuredName":"产品测试1","insuredGender":"M","insuredBirthday":"1988-09-15","insuredCardType":"1","insuredCardNo":"341181198809150011","identityStartDate":"2014-10-24","identityEndDate":"2065-10-24","insuredMobile":"***********","insuredEmail":"<EMAIL>","insuredRelation":"1","isSocialSecurity":"0","height":"175","weight":"120","marriage":"0","insuredIncome":"5.58","insuredNum":"1","occupationCode":"2335","countryCode":"CN","provinceCode":"440000","cityCode":"440100","districtCode":"*********","streetCode":"NYC001-A","address":"天河区珠江新城华夏路10号","insuredZip":"510623","insuredEname":"surename","benefitType":"1"}],"extendInfo":{"ticketType":"1","ticketPrice":"100.25","ticketSuccTime":"2025-05-16 15:31:38","trafficStartTime":"2025-05-16 15:31:38","trafficEndTime":"2025-05-16 17:31:38","ticketNo":"MU6213","trafficCompany":"CA","cityName":"浙江2","departure":"北京1","departureCode":"TGC","destination":"杭州2","destinationCode":"HGH","eTicketNo":"WPNfZ4dRgLPioX3","ticketStatus":"1","trafficType":"空客A13","seatType":"1"},"saleInfo":{"salesSubject":"TC123456","salesCode":"COA2023001","salesType":"1","payAccount":"****************","bankName":"中国银行","ebsCollectionProject":"EBS2023001","payMentAccount":"****************","payMentBankName":"建设银行","ebsProject":"PAY2023001","travelCompany":"国旅","saleStatus":"单售","channelType":1,"channelName":"测试","guestCustomerId":"CUST2023001"}}
#     test_key = "17e8e7205415a0ab9c2d5e26a62e9682"
#     test_partner_id = "TEST1"
    
#     try:
#         # 加密测试数据
#         result = RequestEncryption.encrypt_request(test_data, test_key, test_partner_id)
#         print("加密结果:")
#         print(f"data: {result['data']}")
#         print(f"partnerId: {result['partnerId']}")
#         print(f"sign: {result['sign']}")
#     except Exception as e:
#         print(f"加密失败: {str(e)}") 