# -*- coding: utf-8 -*-
"""
工作流日志工具
提供统一的日志记录功能
"""

import logging
import os
from pathlib import Path
from typing import Optional


class WorkflowLogger:
    """工作流日志记录器"""
    
    _instance: Optional[logging.Logger] = None
    
    @classmethod
    def get_logger(cls, name: str = "workflow", log_level: int = logging.INFO) -> logging.Logger:
        """
        获取日志记录器实例
        :param name: 日志记录器名称
        :param log_level: 日志级别
        :return: 日志记录器实例
        """
        if cls._instance is None:
            cls._instance = cls._create_logger(name, log_level)
        return cls._instance
    
    @classmethod
    def _create_logger(cls, name: str, log_level: int) -> logging.Logger:
        """创建日志记录器"""
        logger = logging.getLogger(name)
        logger.setLevel(log_level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        # 添加处理器到日志记录器
        logger.addHandler(console_handler)
        
        # 可选：添加文件处理器
        try:
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler(
                log_dir / "workflow.log",
                encoding='utf-8'
            )
            file_handler.setLevel(log_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception:
            # 如果无法创建文件处理器，只使用控制台处理器
            pass
        
        return logger


def get_workflow_logger(name: str = "workflow") -> logging.Logger:
    """
    获取工作流日志记录器的便捷函数
    :param name: 日志记录器名称
    :return: 日志记录器实例
    """
    return WorkflowLogger.get_logger(name)


# 兼容原有的logs对象
class LogsCompatibility:
    """兼容原有logs对象的包装类"""
    
    def __init__(self):
        self._logger = get_workflow_logger()
    
    def info(self, message: str):
        """记录信息日志"""
        self._logger.info(message)
    
    def debug(self, message: str):
        """记录调试日志"""
        self._logger.debug(message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self._logger.warning(message)
    
    def error(self, message: str):
        """记录错误日志"""
        self._logger.error(message)
    
    def critical(self, message: str):
        """记录严重错误日志"""
        self._logger.critical(message)


# 创建兼容对象
logs = LogsCompatibility()
