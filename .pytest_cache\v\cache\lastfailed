{"testcase/Delivery service/test_delicer_api.py::TestDeliveryService::test_insure[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_insurance[case_info0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_insurance[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_invoice[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_invoice[base_info1-testcase1]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_invoice_query[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_apply_claim[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_apply_claim[base_info1-testcase1]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_apply_claim[base_info2-testcase2]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_apply_claim[base_info3-testcase3]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_invalidated_invoice[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info0-testcase0]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info1-testcase1]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info2-testcase2]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info3-testcase3]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info4-testcase4]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info5-testcase5]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info6-testcase6]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info7-testcase7]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info8-testcase8]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info9-testcase9]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info10-testcase10]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info11-testcase11]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info12-testcase12]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info13-testcase13]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info14-testcase14]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info15-testcase15]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info16-testcase16]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info17-testcase17]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info18-testcase18]": true, "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic_and_insurance[base_info19-testcase19]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case0]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case1]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case2]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case3]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case4]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case5]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data12]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data13]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data14]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data15]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data16]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data17]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data18]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data19]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data20]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data21]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data22]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data23]": true, "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data24]": true, "services/insurance_workflow/examples/test_simple_example.py": true, "services/insurance_workflow/examples/test_simple_example.py::TestInsuranceWorkflowWithService::test_single_insure_step": true}