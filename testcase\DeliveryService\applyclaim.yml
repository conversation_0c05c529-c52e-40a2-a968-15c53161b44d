- baseInfo:
    api_name: 申请理赔接口
    url: /api/scene/applyclaim
    method: post
    header:
      Content-Type: application/x-www-form-urlencoded
    encrypt_type: TC_Base64
    data_default: deliver/applyclaim
  testCase:
    - case_name: 自主理赔
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respCode: '0'
    - case_name: 微信理赔
      default_select: wx_data_default
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respCode: '0'
    - case_name: 银行卡理赔
      default_select: yhk_data_default
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respCode: '0'
    - case_name: 支付宝理赔
      default_select: zfb_data_default
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respCode: '0'