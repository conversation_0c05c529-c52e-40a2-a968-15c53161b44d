<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1080</width>
    <height>860</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QGroupBox" name="groupBox">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>10</y>
      <width>451</width>
      <height>261</height>
     </rect>
    </property>
    <property name="title">
     <string>baseInfo</string>
    </property>
    <widget class="QLabel" name="label">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>20</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>接口名：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>19</y>
       <width>361</width>
       <height>21</height>
      </rect>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_2">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>49</y>
       <width>361</width>
       <height>21</height>
      </rect>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>50</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>URL：</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_3">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>90</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>请求方法：</string>
     </property>
    </widget>
    <widget class="QComboBox" name="comboBox">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>80</y>
       <width>361</width>
       <height>22</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_4">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>120</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>请求头：</string>
     </property>
    </widget>
    <widget class="QTableWidget" name="tableWidget">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>110</y>
       <width>361</width>
       <height>141</height>
      </rect>
     </property>
     <column>
      <property name="text">
       <string>参数名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>参数值</string>
      </property>
     </column>
    </widget>
    <widget class="QPushButton" name="pushButton">
     <property name="geometry">
      <rect>
       <x>350</x>
       <y>110</y>
       <width>31</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>+</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_2">
     <property name="geometry">
      <rect>
       <x>390</x>
       <y>110</y>
       <width>31</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>-</string>
     </property>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupBox_2">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>270</y>
      <width>451</width>
      <height>541</height>
     </rect>
    </property>
    <property name="title">
     <string>testCase</string>
    </property>
    <widget class="QLabel" name="label_5">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>20</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>用例名：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_3">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>19</y>
       <width>361</width>
       <height>21</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_6">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>50</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>请求参数：</string>
     </property>
    </widget>
    <widget class="QTabWidget" name="tabWidget">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>50</y>
       <width>371</width>
       <height>161</height>
      </rect>
     </property>
     <property name="currentIndex">
      <number>3</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>Tab 1</string>
      </attribute>
      <widget class="QTableWidget" name="tableWidget_2">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>371</width>
         <height>141</height>
        </rect>
       </property>
       <column>
        <property name="text">
         <string>参数名</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>参数值</string>
        </property>
       </column>
      </widget>
      <widget class="QPushButton" name="pushButton_5">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>0</y>
         <width>31</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>-</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_4">
       <property name="geometry">
        <rect>
         <x>280</x>
         <y>0</y>
         <width>31</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>+</string>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>Tab 2</string>
      </attribute>
      <widget class="QTableWidget" name="tableWidget_3">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>371</width>
         <height>141</height>
        </rect>
       </property>
       <column>
        <property name="text">
         <string>参数名</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>参数值</string>
        </property>
       </column>
      </widget>
      <widget class="QPushButton" name="pushButton_7">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>0</y>
         <width>31</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>-</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_6">
       <property name="geometry">
        <rect>
         <x>280</x>
         <y>0</y>
         <width>31</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>+</string>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>页</string>
      </attribute>
      <widget class="QTextEdit" name="textEdit">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>371</width>
         <height>141</height>
        </rect>
       </property>
      </widget>
     </widget>
     <widget class="QWidget" name="tab_4">
      <attribute name="title">
       <string>页</string>
      </attribute>
      <widget class="QTableWidget" name="tableWidget_4">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>0</y>
         <width>371</width>
         <height>101</height>
        </rect>
       </property>
       <column>
        <property name="text">
         <string>参数名</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>参数值</string>
        </property>
       </column>
      </widget>
      <widget class="QPushButton" name="pushButton_3">
       <property name="geometry">
        <rect>
         <x>0</x>
         <y>110</y>
         <width>75</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>浏览</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_9">
       <property name="geometry">
        <rect>
         <x>320</x>
         <y>0</y>
         <width>31</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>-</string>
       </property>
      </widget>
      <widget class="QPushButton" name="pushButton_8">
       <property name="geometry">
        <rect>
         <x>280</x>
         <y>0</y>
         <width>31</width>
         <height>23</height>
        </rect>
       </property>
       <property name="text">
        <string>+</string>
       </property>
      </widget>
      <widget class="QLineEdit" name="lineEdit_9">
       <property name="geometry">
        <rect>
         <x>80</x>
         <y>110</y>
         <width>51</width>
         <height>21</height>
        </rect>
       </property>
      </widget>
      <widget class="QLineEdit" name="lineEdit_10">
       <property name="geometry">
        <rect>
         <x>140</x>
         <y>110</y>
         <width>221</width>
         <height>21</height>
        </rect>
       </property>
      </widget>
     </widget>
    </widget>
    <widget class="QLabel" name="label_7">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>220</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>断言方式：</string>
     </property>
    </widget>
    <widget class="QRadioButton" name="radioButton">
     <property name="geometry">
      <rect>
       <x>80</x>
       <y>220</y>
       <width>89</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>包含断言</string>
     </property>
    </widget>
    <widget class="QRadioButton" name="radioButton_2">
     <property name="geometry">
      <rect>
       <x>180</x>
       <y>220</y>
       <width>89</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>相等断言</string>
     </property>
    </widget>
    <widget class="QTableWidget" name="tableWidget_5">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>240</y>
       <width>371</width>
       <height>121</height>
      </rect>
     </property>
     <column>
      <property name="text">
       <string>参数名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>参数值</string>
      </property>
     </column>
    </widget>
    <widget class="QLabel" name="label_8">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>240</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>断言参数：</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_11">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>240</y>
       <width>31</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>+</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_10">
     <property name="geometry">
      <rect>
       <x>400</x>
       <y>240</y>
       <width>31</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>-</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_9">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>370</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>是否依赖：</string>
     </property>
    </widget>
    <widget class="QRadioButton" name="radioButton_3">
     <property name="geometry">
      <rect>
       <x>80</x>
       <y>370</y>
       <width>89</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>是</string>
     </property>
    </widget>
    <widget class="QRadioButton" name="radioButton_4">
     <property name="geometry">
      <rect>
       <x>180</x>
       <y>370</y>
       <width>89</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>否</string>
     </property>
    </widget>
    <widget class="QTableWidget" name="tableWidget_6">
     <property name="geometry">
      <rect>
       <x>70</x>
       <y>430</y>
       <width>371</width>
       <height>101</height>
      </rect>
     </property>
     <column>
      <property name="text">
       <string>参数名</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>参数值</string>
      </property>
     </column>
    </widget>
    <widget class="QLabel" name="label_10">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>400</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>提取类型：</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_12">
     <property name="geometry">
      <rect>
       <x>400</x>
       <y>430</y>
       <width>31</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>-</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_13">
     <property name="geometry">
      <rect>
       <x>360</x>
       <y>430</y>
       <width>31</width>
       <height>23</height>
      </rect>
     </property>
     <property name="text">
      <string>+</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_16">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>430</y>
       <width>54</width>
       <height>12</height>
      </rect>
     </property>
     <property name="text">
      <string>提取参数：</string>
     </property>
    </widget>
    <widget class="QRadioButton" name="radioButton_5">
     <property name="geometry">
      <rect>
       <x>180</x>
       <y>400</y>
       <width>89</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>提取列表</string>
     </property>
    </widget>
    <widget class="QRadioButton" name="radioButton_6">
     <property name="geometry">
      <rect>
       <x>80</x>
       <y>400</y>
       <width>89</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>提取单个</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_17">
     <property name="geometry">
      <rect>
       <x>290</x>
       <y>400</y>
       <width>141</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>注：支持正则和json提取</string>
     </property>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupBox_3">
    <property name="geometry">
     <rect>
      <x>480</x>
      <y>10</y>
      <width>591</width>
      <height>201</height>
     </rect>
    </property>
    <property name="title">
     <string>配置</string>
    </property>
    <widget class="QLabel" name="label_11">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>30</y>
       <width>101</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>测试用例目录名：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_4">
     <property name="geometry">
      <rect>
       <x>110</x>
       <y>19</y>
       <width>361</width>
       <height>31</height>
      </rect>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_14">
     <property name="geometry">
      <rect>
       <x>480</x>
       <y>20</y>
       <width>75</width>
       <height>31</height>
      </rect>
     </property>
     <property name="text">
      <string>创建</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_15">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>70</y>
       <width>91</width>
       <height>31</height>
      </rect>
     </property>
     <property name="text">
      <string>选择生成路径</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_5">
     <property name="geometry">
      <rect>
       <x>110</x>
       <y>70</y>
       <width>361</width>
       <height>31</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_12">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>120</y>
       <width>101</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>测试用例文件名：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_6">
     <property name="geometry">
      <rect>
       <x>110</x>
       <y>110</y>
       <width>281</width>
       <height>31</height>
      </rect>
     </property>
    </widget>
    <widget class="QLabel" name="label_13">
     <property name="geometry">
      <rect>
       <x>400</x>
       <y>120</y>
       <width>151</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>备注：不需要写文件名后缀</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_14">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>160</y>
       <width>61</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>ip：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_7">
     <property name="geometry">
      <rect>
       <x>190</x>
       <y>160</y>
       <width>171</width>
       <height>21</height>
      </rect>
     </property>
    </widget>
    <widget class="QComboBox" name="comboBox_2">
     <property name="geometry">
      <rect>
       <x>110</x>
       <y>160</y>
       <width>69</width>
       <height>22</height>
      </rect>
     </property>
     <item>
      <property name="text">
       <string>http</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>https</string>
      </property>
     </item>
    </widget>
    <widget class="QLabel" name="label_15">
     <property name="geometry">
      <rect>
       <x>370</x>
       <y>160</y>
       <width>41</width>
       <height>16</height>
      </rect>
     </property>
     <property name="text">
      <string>port：</string>
     </property>
    </widget>
    <widget class="QLineEdit" name="lineEdit_8">
     <property name="geometry">
      <rect>
       <x>410</x>
       <y>160</y>
       <width>61</width>
       <height>21</height>
      </rect>
     </property>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupBox_4">
    <property name="geometry">
     <rect>
      <x>480</x>
      <y>210</y>
      <width>591</width>
      <height>81</height>
     </rect>
    </property>
    <property name="title">
     <string>执行</string>
    </property>
    <widget class="QPushButton" name="pushButton_16">
     <property name="geometry">
      <rect>
       <x>60</x>
       <y>20</y>
       <width>141</width>
       <height>41</height>
      </rect>
     </property>
     <property name="text">
      <string>接口调试</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_17">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>20</y>
       <width>141</width>
       <height>41</height>
      </rect>
     </property>
     <property name="text">
      <string>生成测试用例文件</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_18">
     <property name="geometry">
      <rect>
       <x>400</x>
       <y>20</y>
       <width>141</width>
       <height>41</height>
      </rect>
     </property>
     <property name="text">
      <string>重置</string>
     </property>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupBox_5">
    <property name="geometry">
     <rect>
      <x>480</x>
      <y>300</y>
      <width>591</width>
      <height>511</height>
     </rect>
    </property>
    <property name="title">
     <string>控制台日志</string>
    </property>
    <widget class="QTextEdit" name="textEdit_2">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>20</y>
       <width>581</width>
       <height>461</height>
      </rect>
     </property>
    </widget>
   </widget>
   <widget class="QPushButton" name="pushButton_19">
    <property name="geometry">
     <rect>
      <x>980</x>
      <y>780</y>
      <width>75</width>
      <height>23</height>
     </rect>
    </property>
    <property name="text">
     <string>清空日志</string>
    </property>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1080</width>
     <height>23</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>系统设置</string>
    </property>
    <addaction name="actionhostpeizhi"/>
    <addaction name="actionmorenqingqiutou"/>
   </widget>
   <widget class="QMenu" name="menu_2">
    <property name="title">
     <string>编辑</string>
    </property>
   </widget>
   <widget class="QMenu" name="menu_3">
    <property name="title">
     <string>帮助</string>
    </property>
    <addaction name="actionbangzhuxinxi"/>
   </widget>
   <widget class="QMenu" name="menu_4">
    <property name="title">
     <string>工具</string>
    </property>
    <addaction name="actionMD5jia"/>
    <addaction name="actionbase64"/>
    <addaction name="actionsha1"/>
   </widget>
   <addaction name="menu"/>
   <addaction name="menu_2"/>
   <addaction name="menu_3"/>
   <addaction name="menu_4"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionhostpeizhi">
   <property name="text">
    <string>hostpeizhi</string>
   </property>
  </action>
  <action name="actionmorenqingqiutou">
   <property name="text">
    <string>morenqingqiutou</string>
   </property>
  </action>
  <action name="actionbangzhuxinxi">
   <property name="text">
    <string>bangzhuxinxi</string>
   </property>
  </action>
  <action name="actionMD5jia">
   <property name="text">
    <string>MD5jia</string>
   </property>
  </action>
  <action name="actionbase64">
   <property name="text">
    <string>base64</string>
   </property>
  </action>
  <action name="actionsha1">
   <property name="text">
    <string>sha1</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
