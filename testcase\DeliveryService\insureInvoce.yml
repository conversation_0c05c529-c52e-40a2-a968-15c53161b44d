workflow_testcases:
  - workflow_name: "投保-理赔-多数据组测试"
    description: "完整的投保到理赔流程测试-支持多数据组"
    steps:
      - step_name: "投保"
        api_config:
          api_name: "投保接口"
          url: "/api/scene/insure"
          method: "post"
          header:
            Content-Type: "application/x-www-form-urlencoded"
          encrypt_type: "TC_Base64"
        data_default: "./testcase/default_yaml/deliver/insure.yml"
        data:
          # 退改无忧险测试用例
          - caseName: "投保-退改无忧险-自主赔付-退票"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "90"
          - caseName: "投保-退改无忧险-支付宝-退票"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "90"
          - caseName: "投保-退改无忧险-微信-退票"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "90"
          - caseName: "投保-退改无忧险-银行卡-退票"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "90"
          - caseName: "投保-退改无忧险-支付宝-退票-失败"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "90"
          - caseName: "投保-退改无忧险-自主赔付-改签"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "120"
          - caseName: "投保-退改无忧险-支付宝-改签"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "120"
          - caseName: "投保-退改无忧险-微信-改签"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "120"
          - caseName: "投保-退改无忧险-银行卡-改签"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "120"
          - caseName: "投保-退改无忧险-支付宝-改签-失败"
            planCode: "00200005601"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "120"
          - caseName: "投保-退改无忧险-支付宝-退票"
            planCode: "00200005602"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "90"
          - caseName: "投保-退改无忧险-支付宝-改签-失败"
            planCode: "00200005602"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "120"
          
          # 航班延误险测试用例
          - caseName: "投保-航班延误险-1-2小时-微信-2"
            planCode: "00200005102"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "620"
          - caseName: "投保-航班延误险-2-3小时-微信-2"
            planCode: "00200005102"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "620"
          - caseName: "投保-航班延误险-3-4小时-微信-2"
            planCode: "00200005102"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "620"
          - caseName: "投保-航班延误险-取消-微信-2"
            planCode: "00200005102"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "620"
          - caseName: "投保-航班延误险-返航-微信-2"
            planCode: "00200005102"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "620"
          - caseName: "投保-航班延误险-备降-微信-2"
            planCode: "00200005102"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "620"
          - caseName: "投保-航班延误险-1-2小时-微信-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          - caseName: "投保-航班延误险-2-3小时-微信-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          - caseName: "投保-航班延误险-3-4小时-微信-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          - caseName: "投保-航班延误险-取消-微信-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          - caseName: "投保-航班延误险-返航-微信-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          - caseName: "投保-航班延误险-备降-微信-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          - caseName: "投保-航班延误险-1-2小时-微信-失败-3"
            planCode: "00200005103"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "930"
          
          # 航综1.0测试用例
          - caseName: "投保-航综1.0-1-3小时-微信"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          - caseName: "投保-航综1.0-3-4小时-微信"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          - caseName: "投保-航综1.0-4+小时-微信"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          - caseName: "投保-航综1.0-备降-微信"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          - caseName: "投保-航综1.0-返航-微信"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          - caseName: "投保-航综1.0-返航-微信-失败"
            planCode: "00200005003"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "50"
            sumAssured: "62000990"
          
          # 航综3.0测试用例 - 产品代码4901
          - caseName: "投保-航综3.0-1-3小时-微信"
            planCode: "00200004901"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "3700600"
          - caseName: "投保-航综3.0-3-4小时-微信"
            planCode: "00200004901"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "3700600"
          - caseName: "投保-航综3.0-4+小时-微信"
            planCode: "00200004901"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "3700600"
          - caseName: "投保-航综3.0-备降-微信"
            planCode: "00200004901"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "3700600"
          - caseName: "投保-航综3.0-返航-微信"
            planCode: "00200004901"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "20"
            sumAssured: "3700600"
          
          # 航综3.0测试用例 - 产品代码4902
          - caseName: "投保-航综3.0-1-3小时-微信"
            planCode: "00200004902"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "4201170"
          - caseName: "投保-航综3.0-3-4小时-微信"
            planCode: "00200004902"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "4201170"
          - caseName: "投保-航综3.0-4+小时-微信"
            planCode: "00200004902"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "4201170"
          - caseName: "投保-航综3.0-备降-微信"
            planCode: "00200004902"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "4201170"
          - caseName: "投保-航综3.0-返航-微信"
            planCode: "00200004902"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "30"
            sumAssured: "4201170"
          
          # 航综3.0测试用例 - 产品代码4903
          - caseName: "投保-航综3.0-1-3小时-微信"
            planCode: "00200004903"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "4701185"
          - caseName: "投保-航综3.0-3-4小时-微信"
            planCode: "00200004903"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "4701185"
          - caseName: "投保-航综3.0-4+小时-微信"
            planCode: "00200004903"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "4701185"
          - caseName: "投保-航综3.0-备降-微信"
            planCode: "00200004903"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "4701185"
          - caseName: "投保-航综3.0-返航-微信"
            planCode: "00200004903"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "35"
            sumAssured: "4701185"
          
          # 航综3.0测试用例 - 产品代码4904
          - caseName: "投保-航综3.0-1-3小时-微信"
            planCode: "00200004904"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "40"
            sumAssured: "5201200"
          - caseName: "投保-航综3.0-3-4小时-微信"
            planCode: "00200004904"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "40"
            sumAssured: "5201200"
          - caseName: "投保-航综3.0-4+小时-微信"
            planCode: "00200004904"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "40"
            sumAssured: "5201200"
          - caseName: "投保-航综3.0-备降-微信"
            planCode: "00200004904"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "40"
            sumAssured: "5201200"
          - caseName: "投保-航综3.0-返航-微信"
            planCode: "00200004904"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "40"
            sumAssured: "5201200"
          
          # 航综3.0测试用例 - 产品代码4905
          - caseName: "投保-航综3.0-1-3小时-微信"
            planCode: "00200004905"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "5701410"
          - caseName: "投保-航综3.0-3-4小时-微信"
            planCode: "00200004905"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "5701410"
          - caseName: "投保-航综3.0-4+小时-微信"
            planCode: "00200004905"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "5701410"
          - caseName: "投保-航综3.0-备降-微信"
            planCode: "00200004905"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "5701410"
          - caseName: "投保-航综3.0-返航-微信"
            planCode: "00200004905"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "45"
            sumAssured: "5701410"
          
          # 航综3.0测试用例 - 产品代码4906
          - caseName: "投保-航综3.0-1-3小时-微信"
            planCode: "00200004906"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "60"
            sumAssured: "7201760"
          - caseName: "投保-航综3.0-3-4小时-微信"
            planCode: "00200004906"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "60"
            sumAssured: "7201760"
          - caseName: "投保-航综3.0-4+小时-微信"
            planCode: "00200004906"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "60"
            sumAssured: "7201760"
          - caseName: "投保-航综3.0-备降-微信"
            planCode: "00200004906"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "60"
            sumAssured: "7201760"
          - caseName: "投保-航综3.0-返航-微信"
            planCode: "00200004906"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "60"
            sumAssured: "7201760"
          - caseName: "投保-航综3.0-返航-微信-失败"
            planCode: "00200004906"
            effectTime: "{{$date.now|addMinutes(-1)}}"
            expiryTime: "{{$date.now|addDays(1)}}"
            totalPremium: "60"
            sumAssured: "7201760"
        validation:
          - contains:
              status_code: 200
          - eq:
              $.respCode: "0"
        extract:
          tradeSerialNo: "$.insureData.tradeSerialNo"
          serialNo: "$.insureData.serialNo"
        
      - step_name: "投保状态查询"
        api_config:
          api_name: "投保查询接口"
          url: "/api/scene/query"
          method: "post"
          header:
            Content-Type: "application/x-www-form-urlencoded"
          encrypt_type: "TC_Base64"
        data:
          tradeSerialNo: "{{tradeSerialNo}}"
          serialNo: "{{serialNo}}"
        validation:
          - contains:
              status_code: 200
          - eq:
              $.respCode: '0'
          - eq:
              $.queryData.message: '投保成功'
        extract:
          policyNo: "$.queryData.policyNo"
          sceneCode: "$.queryData.sceneCode"
        max_retry: 3
        retry_interval: 2
        
      - step_name: "申请理赔"
        api_config:
          api_name: "申请理赔接口"
          url: "/api/autoclaim/applyclaim"
          method: "post"
          header:
            Content-Type: "application/x-www-form-urlencoded"
          encrypt_type: "TC_Base64"
        data:
          # 退改无忧险理赔用例 - 退票场景
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "63"
            selfClaimedAmount: "63"
            payType: "0"
            claimTargetInfoList:
              - claimCauseType: "1"
                actualClaimCauseType: "1"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "63"
            selfClaimedAmount: "63"
            payType: "2"
            claimTargetInfoList:
              - claimCauseType: "1"
                actualClaimCauseType: "1"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "63"
            selfClaimedAmount: "63"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "1"
                actualClaimCauseType: "1"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "63"
            selfClaimedAmount: "63"
            payType: "3"
            claimTargetInfoList:
              - claimCauseType: "1"
                actualClaimCauseType: "1"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "180"
            selfClaimedAmount: "180"
            payType: "2"
            claimTargetInfoList:
              - claimCauseType: "1"
                actualClaimCauseType: "1"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 退改无忧险理赔用例 - 改签场景
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "120"
            selfClaimedAmount: "120"
            payType: "0"
            claimTargetInfoList:
              - claimCauseType: "2"
                actualClaimCauseType: "2"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "120"
            selfClaimedAmount: "120"
            payType: "2"
            claimTargetInfoList:
              - claimCauseType: "2"
                actualClaimCauseType: "2"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "120"
            selfClaimedAmount: "120"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "2"
                actualClaimCauseType: "2"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "120"
            selfClaimedAmount: "120"
            payType: "3"
            claimTargetInfoList:
              - claimCauseType: "2"
                actualClaimCauseType: "2"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "180"
            selfClaimedAmount: "180"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "2"
                actualClaimCauseType: "2"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 退改无忧险理赔用例 - 其他场景
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "63"
            selfClaimedAmount: "63"
            payType: "2"
            claimTargetInfoList:
              - claimCauseType: "1"
                actualClaimCauseType: "1"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "2"
            selfClaimedSettleAmount: "180"
            selfClaimedAmount: "180"
            payType: "2"
            claimTargetInfoList:
              - claimCauseType: "2"
                actualClaimCauseType: "2"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航班延误险理赔用例 - 延误1-2小时
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "40"
            selfClaimedAmount: "40"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "60"
            selfClaimedAmount: "60"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航班延误险理赔用例 - 延误2-3小时
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "60"
            selfClaimedAmount: "60"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-121)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "90"
            selfClaimedAmount: "90"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-121)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航班延误险理赔用例 - 延误3-4小时
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "100"
            selfClaimedAmount: "100"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-195)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "150"
            selfClaimedAmount: "150"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-195)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航班延误险理赔用例 - 取消
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "20"
            selfClaimedAmount: "20"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "4"
                actualClaimCauseType: "4"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "30"
            selfClaimedAmount: "30"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "4"
                actualClaimCauseType: "4"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航班延误险理赔用例 - 返航
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "200"
            selfClaimedAmount: "200"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航班延误险理赔用例 - 备降
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "200"
            selfClaimedAmount: "200"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "1"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综1.0理赔用例 - 延误1-3小时
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "60"
            selfClaimedAmount: "60"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综1.0理赔用例 - 延误3-4小时
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "150"
            selfClaimedAmount: "150"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综1.0理赔用例 - 延误4+小时
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "260"
            selfClaimedAmount: "260"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综1.0理赔用例 - 返航
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "260"
            selfClaimedAmount: "260"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综1.0理赔用例 - 备降
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "260"
            selfClaimedAmount: "260"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "700"
            selfClaimedAmount: "700"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综3.0理赔用例 - 产品代码4901 (20元)
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "50"
            selfClaimedAmount: "50"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "100"
            selfClaimedAmount: "100"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "150"
            selfClaimedAmount: "150"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "150"
            selfClaimedAmount: "150"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "150"
            selfClaimedAmount: "150"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综3.0理赔用例 - 产品代码4902 (30元)
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "70"
            selfClaimedAmount: "70"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "200"
            selfClaimedAmount: "200"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综3.0理赔用例 - 产品代码4903 (35元)
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "85"
            selfClaimedAmount: "85"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "200"
            selfClaimedAmount: "200"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综3.0理赔用例 - 产品代码4904 (40元)
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "100"
            selfClaimedAmount: "100"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "200"
            selfClaimedAmount: "200"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "300"
            selfClaimedAmount: "300"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综3.0理赔用例 - 产品代码4905 (45元)
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "110"
            selfClaimedAmount: "110"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "250"
            selfClaimedAmount: "250"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "350"
            selfClaimedAmount: "350"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "350"
            selfClaimedAmount: "350"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "350"
            selfClaimedAmount: "350"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          
          # 航综3.0理赔用例 - 产品代码4906 (60元)
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "160"
            selfClaimedAmount: "160"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-61)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "400"
            selfClaimedAmount: "400"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-190)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "400"
            selfClaimedAmount: "400"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "3"
                actualClaimCauseType: "3"
                trafficStartTime: "{{$date.now|addMinutes(-245)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "400"
            selfClaimedAmount: "400"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "5"
                actualClaimCauseType: "5"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "400"
            selfClaimedAmount: "400"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
          - autoClaimBizType: "5"
            selfClaimedSettleAmount: "500"
            selfClaimedAmount: "500"
            payType: "1"
            claimTargetInfoList:
              - claimCauseType: "6"
                actualClaimCauseType: "6"
                trafficStartTime: "{{$date.now|addHours(-2)}}"
                actualTrafficStartTime: "{{$date.now}}"
        validation:
          - contains:
              status_code: 200
          - eq:
              $.respCode: '0'

