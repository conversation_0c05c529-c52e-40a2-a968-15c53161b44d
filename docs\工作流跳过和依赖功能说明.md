# 工作流跳过执行和依赖等待功能说明

## 功能概述

本文档描述工作流测试框架新增的两个核心功能：
1. **skip_execution**: 跳过执行功能
2. **Forwardsign**: 步骤依赖等待功能

## 1. skip_execution 跳过执行功能

### 功能描述
当测试用例中包含 `skip_execution: true` 时，该测试步骤将被跳过不执行。同时，如果下一个步骤是通用接口（没有具体业务数据），则会一起跳过。

### 使用方法

#### 基本用法
```yaml
data:
  - caseName: "正常执行的用例"
    planCode: "00200005003"
    effectTime: "{{$date.now|addMinutes(-1)}}"
    
  - caseName: "跳过执行的用例" 
    skip_execution: true
    planCode: "00200005004"
    effectTime: "{{$date.now|addMinutes(-1)}}"
```

#### 工作原理
1. 框架检测到 `skip_execution: true`
2. 记录跳过日志并标记该步骤跳过
3. 检查下一个步骤是否为通用接口：
   - 如果是通用接口（主要依赖变量传递），则一起跳过
   - 如果不是通用接口，则只跳过当前步骤

#### 通用接口判断标准
下一个步骤被认为是通用接口，当满足以下条件之一：
- 没有数据字段
- 数据字段为空
- 数据字段全部是变量引用（如 `{{variableName}}`）

### 应用场景
- **调试测试**: 临时跳过某些有问题的测试用例
- **环境限制**: 某些环境不支持特定的业务场景
- **分阶段测试**: 分批次执行不同的测试用例组合

## 2. Forwardsign 依赖等待功能

### 功能描述
当测试步骤中包含 `Forwardsign` 配置时，该步骤会等待指定的前置步骤完成后再执行，实现步骤间的依赖控制。

### 使用方法

#### 基本语法
```yaml
data:
  - Forwardsign: "case:X,step:Y"  # 等待用例X的步骤Y完成
    # 其他业务数据...
```

#### 具体示例
```yaml
steps:
  - step_name: "投保"           # 步骤0
    data:
      - caseName: "投保用例1"   # 用例0
        planCode: "00200005003"
      - caseName: "投保用例2"   # 用例1  
        planCode: "00200005004"
        
  - step_name: "投保状态查询"   # 步骤1
    data:
      - tradeSerialNo: "{{tradeSerialNo}}"  # 用例0
      - Forwardsign: "case:0,step:0"        # 用例1，等待用例0的步骤0完成
        tradeSerialNo: "{{tradeSerialNo}}"
        
  - step_name: "申请理赔"       # 步骤2
    data:
      - autoClaimBizType: "5"               # 用例0
      - Forwardsign: "case:0,step:2"        # 用例1，等待用例0的步骤2完成
        autoClaimBizType: "6"
```

#### 参数说明
- **case:X**: 用例索引，从0开始计数
- **step:Y**: 步骤索引，从0开始计数
- **格式**: 必须严格按照 `case:数字,step:数字` 的格式

### 工作原理
1. **解析依赖**: 正则表达式解析 `Forwardsign` 格式
2. **状态跟踪**: 维护类级别的步骤完成状态字典和上下文ID映射
3. **轮询等待**: 每秒检查一次目标步骤是否完成
4. **获取上下文**: 步骤完成后获取该步骤的上下文ID
5. **变量替换**: 使用依赖步骤的上下文ID进行变量替换，支持fallback机制
6. **超时控制**: 默认最大等待时间300秒（5分钟）
7. **线程安全**: 使用线程锁确保状态更新的原子性

### 上下文变量传递机制
当使用 `Forwardsign` 等待依赖步骤时，框架会：
1. 等待目标步骤完成
2. 获取目标步骤的上下文ID
3. 优先使用目标步骤的上下文进行变量替换
4. 如果某些变量在目标上下文中不存在，则从当前步骤的上下文中获取（fallback机制）

#### 变量替换优先级
```yaml
data:
  - Forwardsign: "case:0,step:1"
    tradeSerialNo: "{{tradeSerialNo}}"    # 优先从case:0,step:1的上下文获取
    currentStepVar: "{{localVariable}}"   # 如果上述上下文没有，则从当前上下文获取
```

### 技术实现细节

#### 状态管理
```python
class TestDeliverWorkflow:
    # 类级别的步骤完成状态跟踪
    _step_completion_status = {}
    # 类级别的上下文ID映射：记录每个步骤对应的上下文ID
    _step_context_mapping = {}
    _status_lock = threading.Lock()
    
    @classmethod
    def _mark_step_completed(cls, case_index, step_index, context_id):
        """标记步骤完成并记录上下文ID"""
        with cls._status_lock:
            key = f"case:{case_index},step:{step_index}"
            cls._step_completion_status[key] = True
            cls._step_context_mapping[key] = context_id
```

#### 等待机制
```python
@classmethod
def _wait_for_step_completion(cls, forwardsign, max_wait_time=300):
    """等待指定步骤完成并返回该步骤的上下文ID"""
    # 解析目标步骤
    pattern = r'case:(\d+),step:(\d+)'
    match = re.match(pattern, forwardsign)
    
    # 轮询等待
    while time.time() - wait_start_time < max_wait_time:
        with cls._status_lock:
            if cls._step_completion_status.get(wait_key, False):
                target_context_id = cls._step_context_mapping.get(wait_key)
                return target_context_id  # 返回上下文ID
        time.sleep(1)
    
    return None  # 超时
```

#### 变量替换增强
```python
def replace_load_with_context(self, data, context_id, fallback_context_id=None):
    """支持fallback上下文的变量替换"""
    for var_name in context_matches:
        # 首先尝试从主要上下文获取变量
        var_value = self.get_context_variable(context_id, var_name)
        
        # 如果没有找到，且有fallback上下文，则从fallback获取
        if var_value is None and fallback_context_id:
            var_value = self.get_context_variable(fallback_context_id, var_name)
```

## 3. 组合使用示例

### 复杂业务场景
```yaml
workflow_testcases:
  - workflow_name: "投保-理赔-多数据组测试"
    description: "支持跳过和依赖的完整流程测试，包含上下文变量传递"
    steps:
      - step_name: "投保"
        data:
          - caseName: "正常投保用例"
            planCode: "00200005003"
            # 投保成功后会提取 tradeSerialNo, serialNo
          - caseName: "问题投保用例"
            skip_execution: true
            planCode: "00200005004"
          - caseName: "延迟投保用例"  
            planCode: "00200005005"
        extract:
          tradeSerialNo: "$.insureData.tradeSerialNo"
          serialNo: "$.insureData.serialNo"
            
      - step_name: "投保状态查询"
        data:
          - tradeSerialNo: "{{tradeSerialNo}}"  # 来自本case的投保步骤
            serialNo: "{{serialNo}}"
          - tradeSerialNo: "{{tradeSerialNo}}"  # 跳过，因为对应的投保被跳过
            serialNo: "{{serialNo}}"
          - Forwardsign: "case:0,step:0"        # 等待第一个投保完成
            tradeSerialNo: "{{tradeSerialNo}}"  # 来自case:0,step:0的上下文
            serialNo: "{{serialNo}}"            # 来自case:0,step:0的上下文
        extract:
          policyNo: "$.queryData.policyNo"
          sceneCode: "$.queryData.sceneCode"
            
      - step_name: "申请理赔"
        data:
          - autoClaimBizType: "5"
            policyNo: "{{policyNo}}"      # 来自本case的查询步骤
          - autoClaimBizType: "5"         # 跳过
          - Forwardsign: "case:0,step:1"  # 等待第一个查询完成
            autoClaimBizType: "6"
            policyNo: "{{policyNo}}"      # 来自case:0,step:1的上下文
            sceneCode: "{{sceneCode}}"    # 来自case:0,step:1的上下文
            tradeSerialNo: "{{tradeSerialNo}}"  # fallback到当前上下文或case:0,step:0
```

## 4. 注意事项

### skip_execution 注意事项
1. **变量传递**: 跳过的步骤不会提取变量，可能影响后续步骤
2. **通用步骤判断**: 确保通用步骤的判断逻辑符合业务需求
3. **日志记录**: 所有跳过操作都会记录详细日志

### Forwardsign 注意事项
1. **格式严格**: 必须严格按照 `case:数字,step:数字` 格式
2. **索引从0开始**: 用例和步骤索引都是从0开始计数
3. **避免死锁**: 不要创建循环依赖
4. **超时设置**: 默认5分钟超时，可根据需要调整
5. **线程安全**: 在并发测试中保证状态同步

### 最佳实践
1. **合理规划依赖**: 避免过于复杂的依赖关系
2. **错误处理**: 为依赖等待失败准备备选方案
3. **测试调试**: 使用跳过功能进行分步调试
4. **文档维护**: 及时更新测试用例的依赖关系文档

## 5. 故障排查

### 常见问题
1. **Forwardsign格式错误**: 检查是否严格按照格式要求
2. **等待超时**: 检查目标步骤是否真的完成
3. **变量丢失**: 跳过步骤导致变量没有提取
4. **并发冲突**: 多线程执行时的状态同步问题

### 调试方法
1. **查看日志**: 详细的执行和跳过日志
2. **状态检查**: 检查步骤完成状态字典
3. **逐步调试**: 使用 skip_execution 逐步隔离问题

## 6. 版本更新日志

### v1.1 (当前版本)
- 新增 skip_execution 跳过执行功能
- 新增 Forwardsign 依赖等待功能
- 支持通用步骤自动跳过
- 实现线程安全的状态管理
- **新增**: 依赖步骤的上下文ID获取和变量传递
- **新增**: Fallback上下文机制，支持多层级变量查找
- **增强**: 变量替换优先级控制
- 添加详细的日志记录和错误处理
