import os
import platform
import sys
import allure
from pathlib import Path

def update_environment_xml(env_name, env_config, report_dir=None):
    """
    更新environment.xml文件中的环境变量
    :param env_name: 环境名称
    :param env_config: 环境配置
    :param report_dir: 报告目录路径，如果为None则使用默认路径
    """
    try:
        # 读取模板文件
        template_file = './environment.xml'
        if not os.path.exists(template_file):
            print(f"警告: 模板文件不存在: {template_file}")
            return None
            
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换环境变量
        content = content.replace('${ENV_NAME}', env_name)
        content = content.replace('${BASE_URL}', env_config.get('base_url', ''))
        content = content.replace('${PARTNER_ID}', env_config.get('partner_id', ''))
        content = content.replace('${returnUrl}', env_config.get('returnUrl', ''))

        # 获取 Python 版本
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        content = content.replace('${PYTHON_VERSION}', python_version)
        
        # 获取 Allure 版本
        try:
            import subprocess
            result = subprocess.run('allure --version', shell=True, capture_output=True, text=True)
            allure_version = result.stdout.strip() if result.returncode == 0 else 'unknown'
        except:
            allure_version = 'unknown'
        content = content.replace('${ALLURE_VERSION}', allure_version)
        
        # 获取操作系统信息
        os_info = f"{platform.system()} {platform.release()}"
        content = content.replace('${OS_INFO}', os_info)
        
        # 获取当前时间
        from datetime import datetime
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        content = content.replace('${CURRENT_TIME}', current_time)
        
        # 确定目标文件路径
        if report_dir:
            # 使用传入的报告目录
            temp_file = Path(report_dir) / 'environment.xml'
        else:
            # 使用默认路径
            temp_file = Path('./report/temp/environment.xml')
        
        # 确保目录存在
        temp_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"成功更新environment.xml: {temp_file}")
        return str(temp_file)
        
    except Exception as e:
        print(f"\n警告: 更新environment.xml失败: {str(e)}")
        return None