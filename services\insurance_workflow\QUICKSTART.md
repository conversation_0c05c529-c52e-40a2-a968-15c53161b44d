# 快速开始指南

## 5分钟上手保险理赔工作流服务

### 第一步：复制服务模块

将 `services/insurance_workflow/` 目录复制到你的项目根目录下。

### 第二步：最简单的使用

创建一个新的测试文件或修改现有的测试文件：

```python
# test_simple_workflow.py
from services.insurance_workflow import execute_insurance_claim_workflow

def test_insurance_workflow():
    """最简单的工作流测试"""
    # 一行代码执行完整工作流
    results = execute_insurance_claim_workflow('testcase/DeliveryService/insureInvoce copy.yml')
    
    # 验证结果
    for result in results:
        assert result.success, f"工作流执行失败: {result.error_message}"
        print(f"✅ {result.workflow_name} 执行成功")
```

### 第三步：运行测试

```bash
pytest test_simple_workflow.py -v
```

## 替换现有的复杂测试

如果你现在有这样的复杂测试代码：

```python
# 原有的复杂代码（400+ 行）
class TestDeliverWorkflow:
    def setup_class(self):
        self.context_worker = context_worker()
        # 大量初始化代码...
    
    @pytest.mark.parametrize('steps_data', ReadYamlData().get_workflow_yaml(...))
    def test_insurance_claim_workflow(self, steps_data):
        # 400+ 行复杂的实现
        if not steps_data:
            pytest.skip("工作流用例数据为空，跳过测试")
        
        # 大量的业务逻辑代码...
        for step_index, (workflow_info, base_info, testcase) in enumerate(steps_data):
            # 复杂的步骤执行逻辑...
```

可以直接替换为：

```python
# 新的简化代码（5-10 行）
class TestDeliverWorkflow:
    def test_insurance_claim_workflow(self):
        """简化后的工作流测试"""
        results = execute_insurance_claim_workflow('testcase/DeliveryService/insureInvoce copy.yml')
        
        for result in results:
            assert result.success, f"工作流执行失败: {result.error_message}"
```

## 常用使用模式

### 模式1：基础测试类

```python
import pytest
from services.insurance_workflow import create_workflow_service

class TestInsuranceWorkflow:
    def setup_class(self):
        self.workflow_service = create_workflow_service()
    
    def teardown_class(self):
        self.workflow_service.cleanup_all_contexts()
    
    def test_complete_workflow(self):
        results = self.workflow_service.execute_workflow_from_yaml(
            'testcase/DeliveryService/insureInvoce copy.yml'
        )
        for result in results:
            assert result.success
```

### 模式2：参数化测试

```python
@pytest.mark.parametrize('yaml_file', [
    'testcase/DeliveryService/insureInvoce copy.yml',
    'testcase/DeliveryService/other_workflow.yml',
])
def test_multiple_workflows(yaml_file):
    results = execute_insurance_claim_workflow(yaml_file)
    for result in results:
        assert result.success
```

### 模式3：单步骤测试

```python
def test_single_insure_step():
    service = create_workflow_service()
    
    step_config = {
        'step_name': '投保测试',
        'api_config': {
            'api_name': '投保接口',
            'url': '/api/scene/insure',
            'method': 'post'
        },
        'data': {'planCode': '00200005003'}
    }
    
    step_result, context_id = service.execute_single_step(step_config)
    assert step_result.success
```

## 常见问题快速解决

### Q1: 找不到YAML文件
**A**: 确保YAML文件路径相对于项目根目录，例如：
```python
# 正确的路径
'testcase/DeliveryService/insureInvoce copy.yml'

# 错误的路径
'./testcase/DeliveryService/insureInvoce copy.yml'  # 不要加 ./
'/absolute/path/to/file.yml'  # 避免绝对路径
```

### Q2: API请求失败
**A**: 检查基础URL配置：
```python
service = create_workflow_service()
service.request_handler.set_base_url('http://your-api-server.com')
```

### Q3: 变量替换不生效
**A**: 检查变量格式：
```python
# 正确格式
'{{tradeSerialNo}}'  # 上下文变量
'{{$date.now}}'      # 动态变量

# 错误格式
'{tradeSerialNo}'    # 缺少大括号
'${tradeSerialNo}'   # 错误的语法
```

### Q4: 并发测试冲突
**A**: 为每个测试使用独立的服务实例：
```python
class TestWorkflow:
    def setup_method(self):  # 注意：使用 setup_method 而不是 setup_class
        self.service = create_workflow_service()
    
    def teardown_method(self):
        self.service.cleanup_all_contexts()
```

## 下一步

1. **阅读完整文档**：查看 `README.md` 了解详细功能
2. **查看示例代码**：参考 `examples/` 目录下的示例
3. **自定义配置**：根据项目需求调整配置
4. **集成到CI/CD**：将新的测试集成到持续集成流程

## 获得帮助

- 查看 `examples/usage_examples.py` 获取更多使用示例
- 查看 `README.md` 获取完整文档
- 检查日志输出获取详细的错误信息

---

🎉 恭喜！你已经成功上手保险理赔工作流服务。现在可以享受简化后的测试代码带来的便利了！
