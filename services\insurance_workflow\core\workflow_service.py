# -*- coding: utf-8 -*-
"""
保险理赔工作流服务
提供完整的保险理赔工作流执行功能
"""

import time
import threading
from typing import List, Dict, Any, Optional, Tuple
import allure

from services.insurance_workflow.config.workflow_config import WorkflowConfig, WorkflowConfigManager
from services.insurance_workflow.core.context_manager import WorkflowContextManager
from services.insurance_workflow.core.step_executor import StepExecutor, StepExecutionResult
from services.insurance_workflow.core.step_manager import CrossProcessStepManager
from services.insurance_workflow.utils.request_handler import WorkflowRequestHandler
from services.insurance_workflow.utils.variable_processor import WorkflowVariableProcessor
from services.insurance_workflow.utils.logger import get_workflow_logger


class WorkflowExecutionResult:
    """工作流执行结果"""
    
    def __init__(self, success: bool, workflow_name: str, context_id: str):
        self.success = success
        self.workflow_name = workflow_name
        self.context_id = context_id
        self.step_results: List[StepExecutionResult] = []
        self.error_message = ""
        self.execution_time = 0
        self.start_time = time.time()
    
    def add_step_result(self, result: StepExecutionResult):
        """添加步骤执行结果"""
        self.step_results.append(result)
    
    def finish(self, success: bool = True, error_message: str = ""):
        """完成工作流执行"""
        self.success = success
        self.error_message = error_message
        self.execution_time = time.time() - self.start_time


class InsuranceClaimWorkflowService:
    """保险理赔工作流服务"""
    
    # 类级别的跨进程步骤管理器
    _step_manager: Optional[CrossProcessStepManager] = None
    _setup_lock = threading.Lock()
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化工作流服务
        :param project_root: 项目根目录路径
        """
        self.logger = get_workflow_logger()
        
        # 初始化各个组件
        self.config_manager = WorkflowConfigManager(project_root)
        self.context_manager = WorkflowContextManager(project_root)
        self.request_handler = WorkflowRequestHandler(project_root)
        self.variable_processor = WorkflowVariableProcessor()
        self.step_executor = StepExecutor(
            self.context_manager,
            self.request_handler,
            self.variable_processor
        )
        
        # 初始化跨进程步骤管理器
        with InsuranceClaimWorkflowService._setup_lock:
            if InsuranceClaimWorkflowService._step_manager is None:
                InsuranceClaimWorkflowService._step_manager = CrossProcessStepManager("insure_claim_workflow")
    
    def execute_workflow_from_yaml(self, yaml_file_path: str) -> List[WorkflowExecutionResult]:
        """
        从YAML文件执行工作流
        :param yaml_file_path: YAML文件路径
        :return: 工作流执行结果列表
        """
        try:
            # 加载工作流配置
            workflow_steps_list = self.config_manager.load_workflow_from_yaml(yaml_file_path)
            
            if not workflow_steps_list:
                self.logger.error("工作流用例数据为空，跳过测试")
                return []
            
            results = []
            
            # 执行每个工作流用例
            for workflow_steps in workflow_steps_list:
                if not workflow_steps:
                    continue
                
                # 创建工作流配置
                workflow_config = self.config_manager.create_workflow_config(workflow_steps)
                
                # 执行工作流
                result = self.execute_workflow(workflow_config)
                results.append(result)
            
            return results
            
        except Exception as e:
            self.logger.error(f"从YAML文件执行工作流失败: {e}")
            raise e
    
    def execute_workflow(self, workflow_config: WorkflowConfig) -> WorkflowExecutionResult:
        """
        执行工作流
        :param workflow_config: 工作流配置
        :return: 工作流执行结果
        """
        workflow_name = workflow_config.workflow_name
        self.logger.info(f"开始执行工作流: {workflow_name}")
        
        # 设置Allure标题和描述
        allure.dynamic.title(workflow_name)
        allure.attach(workflow_config.description or '无描述', '工作流描述', allure.attachment_type.TEXT)
        
        # 创建工作流上下文
        context_id = self.context_manager.create_workflow_context(
            workflow_name, workflow_config.case_index
        )
        
        # 创建执行结果对象
        result = WorkflowExecutionResult(True, workflow_name, context_id)
        
        try:
            # 执行工作流中的每一步
            for step_index, step_config in enumerate(workflow_config.steps):
                # 处理前置依赖等待
                target_context_id = None
                if step_config.forwardsign:
                    target_context_id = self._wait_for_dependency(step_config, workflow_config.case_index)
                    if target_context_id is None:
                        self.logger.error(f"步骤 {step_config.step_name} 的前置依赖等待失败，跳过执行")
                        continue
                
                # 执行步骤
                step_result = self.step_executor.execute_step(
                    step_config, context_id, target_context_id
                )
                
                # 添加步骤结果
                result.add_step_result(step_result)
                
                # 如果步骤执行失败，停止工作流
                if not step_result.success:
                    result.finish(False, f"步骤 {step_config.step_name} 执行失败: {step_result.error}")
                    return result
                
                # 标记步骤完成
                self._mark_step_completed(
                    workflow_config.case_index, step_index, context_id
                )
            
            # 工作流执行成功
            result.finish(True)
            self.logger.info(f"工作流 {workflow_name} 所有步骤执行成功")
            
        except Exception as e:
            error_msg = f"工作流执行异常: {e}"
            self.logger.error(error_msg)
            result.finish(False, error_msg)
        
        return result
    
    def execute_single_step(self, step_config_dict: Dict[str, Any], 
                           context_id: Optional[str] = None) -> Tuple[StepExecutionResult, str]:
        """
        执行单个步骤（用于独立测试）
        :param step_config_dict: 步骤配置字典
        :param context_id: 上下文ID，如果为None则创建新的
        :return: (步骤执行结果, 上下文ID)
        """
        # 创建步骤配置对象
        from services.insurance_workflow.config.workflow_config import StepConfig
        step_config = StepConfig(**step_config_dict)
        
        # 创建或使用现有上下文
        if context_id is None:
            context_id = self.context_manager.create_workflow_context(
                step_config.step_name, 0
            )
        
        # 执行步骤
        result = self.step_executor.execute_step(step_config, context_id)
        
        return result, context_id
    
    def get_context_variables(self, context_id: str) -> Dict[str, Any]:
        """
        获取上下文中的所有变量
        :param context_id: 上下文ID
        :return: 变量字典
        """
        context = self.context_manager.get_context(context_id)
        if context:
            return context.variables.copy()
        return {}
    
    def set_context_variable(self, context_id: str, key: str, value: Any) -> bool:
        """
        设置上下文变量
        :param context_id: 上下文ID
        :param key: 变量名
        :param value: 变量值
        :return: 是否设置成功
        """
        return self.context_manager.set_variable(context_id, key, value)
    
    def cleanup_context(self, context_id: str) -> bool:
        """
        清理指定的上下文
        :param context_id: 上下文ID
        :return: 是否清理成功
        """
        return self.context_manager.cleanup_context(context_id)
    
    def cleanup_all_contexts(self) -> int:
        """
        清理当前工作线程的所有上下文
        :return: 清理的上下文数量
        """
        return self.context_manager.cleanup_worker_contexts()
    
    def _wait_for_dependency(self, step_config, case_index: int) -> Optional[str]:
        """
        等待前置依赖步骤完成
        :param step_config: 步骤配置
        :param case_index: 用例索引
        :return: 依赖步骤的上下文ID
        """
        if not step_config.forwardsign:
            return None
        
        self.logger.info(f"步骤 {step_config.step_name} 检测到前置依赖: {step_config.forwardsign}")
        
        # 等待依赖步骤完成并获取其上下文ID
        if self._step_manager:
            target_context_id = self._step_manager.wait_for_step_completion(step_config.forwardsign)
            if target_context_id:
                self.logger.info(f"步骤 {step_config.step_name} 获取到依赖步骤的上下文ID: {target_context_id}")
                return target_context_id
            else:
                self.logger.error(f"步骤 {step_config.step_name} 的前置依赖 {step_config.forwardsign} 等待超时或失败")
        
        return None
    
    def _mark_step_completed(self, case_index: int, step_index: int, context_id: str):
        """
        标记步骤完成
        :param case_index: 用例索引
        :param step_index: 步骤索引
        :param context_id: 上下文ID
        """
        if self._step_manager:
            self._step_manager.mark_step_completed(case_index, step_index, context_id)
    
    @classmethod
    def cleanup_step_manager(cls):
        """清理跨进程步骤管理器"""
        with cls._setup_lock:
            if cls._step_manager is not None:
                cls._step_manager.cleanup()
                cls._step_manager = None


# 便捷函数
def create_workflow_service(project_root: Optional[str] = None) -> InsuranceClaimWorkflowService:
    """
    创建工作流服务实例
    :param project_root: 项目根目录路径
    :return: 工作流服务实例
    """
    return InsuranceClaimWorkflowService(project_root)


def execute_insurance_claim_workflow(yaml_file_path: str, 
                                   project_root: Optional[str] = None) -> List[WorkflowExecutionResult]:
    """
    执行保险理赔工作流的便捷函数
    :param yaml_file_path: YAML文件路径
    :param project_root: 项目根目录路径
    :return: 工作流执行结果列表
    """
    service = create_workflow_service(project_root)
    return service.execute_workflow_from_yaml(yaml_file_path)
