# -*- coding: utf-8 -*-
"""
请求处理器
提供API请求执行和响应处理功能
"""

import json
import traceback
import requests
import urllib3
from typing import Dict, Any, Optional
import allure

from services.insurance_workflow.utils.logger import get_workflow_logger
from services.insurance_workflow.utils.variable_processor import WorkflowVariableProcessor

# 可选依赖，如果不存在则使用默认实现
try:
    from common.encryption import RequestEncryption
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False

try:
    from conf.env_manager import env_manager
    ENV_MANAGER_AVAILABLE = True
except ImportError:
    ENV_MANAGER_AVAILABLE = False


class WorkflowRequestHandler:
    """工作流请求处理器"""
    
    def __init__(self, project_root: Optional[str] = None):
        """
        初始化请求处理器
        :param project_root: 项目根目录路径
        """
        self.logger = get_workflow_logger()
        self.variable_processor = WorkflowVariableProcessor()

        # 初始化加密组件（如果可用）
        if ENCRYPTION_AVAILABLE:
            self.RequestEncryption = RequestEncryption()
        else:
            self.RequestEncryption = None

        # 初始化环境配置（如果可用）
        if ENV_MANAGER_AVAILABLE:
            try:
                self.env_config = env_manager.get_current_env_config()
            except Exception:
                self.env_config = {}
        else:
            self.env_config = {}

        # 默认配置
        self.default_config = {
            'base_url': self.env_config.get('base_url', 'https://api-uat.itcis.cn'),  # 默认基础URL
            'timeout': 30,
            'verify_ssl': False
        }
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    def execute_request(self, api_config: Dict[str, Any], testcase: Dict[str, Any], 
                       context_id: str) -> requests.Response:
        """
        执行API请求
        :param api_config: API配置信息
        :param testcase: 测试用例数据
        :param context_id: 上下文ID
        :return: 响应对象
        """
        try:
            # 准备请求参数
            url = self._build_url(api_config.get('url', ''))
            method = api_config.get('method', 'GET').upper()
            headers = self._process_headers(api_config.get('header', {}))
            encrypt_type = api_config.get('encrypt_type')
            
            # 记录请求信息
            case_info_text = (
                f'请求URL：{url}\n'
                f'接口名称：{api_config.get("api_name", "未知接口")}\n'
                f'请求方法：{method}\n'
                f'请求头：{headers}\n'
                f'加密类型：{encrypt_type}\n'
            )
            allure.attach(case_info_text, '接口请求信息', allure.attachment_type.TEXT)
            
            # 处理请求数据
            request_data = self._prepare_request_data(testcase, method)
            
            # 执行请求
            response = self._send_request(method, url, headers, request_data, encrypt_type)
            
            # 处理响应
            self._process_response(response, api_config.get('api_name', '未知接口'))
            
            return response
            
        except Exception as e:
            self.logger.error(f"执行API请求失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            raise e
    
    def _build_url(self, url_path: str) -> str:
        """构建完整的URL"""
        if url_path.startswith('http'):
            return url_path
        
        base_url = self.default_config['base_url']
        if not url_path.startswith('/'):
            url_path = '/' + url_path
        
        return base_url + url_path
    
    def _process_headers(self, headers: Dict[str, Any]) -> Dict[str, str]:
        """处理请求头"""
        processed_headers = {}
        for key, value in headers.items():
            processed_headers[key] = str(value)
        return processed_headers
    
    def _prepare_request_data(self, testcase: Dict[str, Any], method: str) -> Dict[str, Any]:
        """准备请求数据"""
        request_data = {}
        
        # 获取测试用例数据
        case_data = testcase.get('data', {})
        
        # 根据请求方法处理数据
        if method in ['POST', 'PUT', 'PATCH']:
            if case_data:
                request_data['data'] = case_data
        elif method == 'GET':
            if case_data:
                request_data['params'] = case_data
        
        return request_data
    
    def _send_request(self, method: str, url: str, headers: Dict[str, str], 
                     request_data: Dict[str, Any], encrypt_type: Optional[str]) -> requests.Response:
        """发送HTTP请求"""
        try:
            # 处理加密（如果需要）
            if encrypt_type:
                request_data = self._handle_encryption(request_data, encrypt_type)

            # 发送请求
            if method == 'GET':
                response = requests.get(
                    url,
                    params=request_data.get('params'),
                    headers=headers,
                    timeout=self.default_config['timeout'],
                    verify=self.default_config['verify_ssl']
                )
            elif method == 'POST':
                response = requests.post(
                    url,
                    data=request_data.get('data'),
                    headers=headers,
                    timeout=self.default_config['timeout'],
                    verify=self.default_config['verify_ssl']
                )
            elif method == 'PUT':
                response = requests.put(
                    url,
                    data=request_data.get('data'),
                    headers=headers,
                    timeout=self.default_config['timeout'],
                    verify=self.default_config['verify_ssl']
                )
            elif method == 'DELETE':
                response = requests.delete(
                    url,
                    headers=headers,
                    timeout=self.default_config['timeout'],
                    verify=self.default_config['verify_ssl']
                )
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
            
            return response
            
        except requests.RequestException as e:
            self.logger.error(f"HTTP请求异常: {e}")
            raise e
        except Exception as e:
            self.logger.error(f"发送请求时发生未知错误: {e}")
            raise e
    
    def _handle_encryption(self, request_data: Dict[str, Any], encrypt_type: str) -> Dict[str, Any]:
        """处理数据加密"""
        if encrypt_type == 'TC_Base64':
            if ENCRYPTION_AVAILABLE and self.RequestEncryption:
                # 处理Base64加密
                try:
                    request_data = self.RequestEncryption.encrypt_request_TC_Base64(
                        request_data,
                        self.env_config.get('key', ''),
                        self.env_config.get('partner_id', '')
                    )
                    self.logger.info(f"应用加密类型: {encrypt_type} 加密返回数据：{request_data}")
                except Exception as e:
                    self.logger.error(f"加密处理失败: {e}")
            else:
                self.logger.warning(f"加密组件不可用，跳过加密处理: {encrypt_type}")

        return request_data
    
    def _process_response(self, response: requests.Response, api_name: str):
        """处理响应"""
        try:
            # 记录响应状态码
            status_code = response.status_code
            
            # 尝试解析JSON响应
            try:
                response_json = response.json()
                response_text = json.dumps(response_json, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                response_text = response.text
                response_json = None
            
            # 添加Allure附件
            allure.attach(response_text, '接口响应信息', allure.attachment_type.TEXT)
            
            self.logger.info(f"API {api_name} 响应状态码: {status_code}")
            
            # 检查响应状态
            if status_code >= 400:
                self.logger.warning(f"API {api_name} 返回错误状态码: {status_code}")
            
        except Exception as e:
            self.logger.error(f"处理响应时发生错误: {e}")
    
    def get_template_data_by_pay_type(self, template_path: str, pay_type: Optional[int] = None, 
                                     template_key: Optional[str] = None) -> Dict[str, Any]:
        """
        根据支付类型或模板key获取默认数据模板
        :param template_path: 模板文件路径
        :param pay_type: 支付类型 0-自主赔付 1-微信 2-支付宝 3-银行卡
        :param template_key: 直接指定模板key
        :return: 对应的默认数据
        """
        try:
            from services.insurance_workflow.utils.yaml_reader import WorkflowYamlReader
            
            yaml_reader = WorkflowYamlReader()
            template_data = yaml_reader.read_yaml_data(template_path)
            
            if not isinstance(template_data, dict) or not template_data:
                self.logger.error(f"模板数据格式错误或为空: {template_data}")
                return {}
            
            if template_key:
                # 直接使用指定的模板key
                return template_data.get(template_key, {})
            
            if pay_type is not None:
                # 根据支付类型映射到对应的数据模板
                pay_type_mapping = {
                    0: 'data_default',      # 自主赔付
                    1: 'wx_data_default',   # 微信赔付
                    2: 'zfb_data_default',  # 支付宝赔付
                    3: 'yhk_data_default'   # 银行卡赔付
                }
                template_key = pay_type_mapping.get(pay_type, 'data_default')
                self.logger.info(f"根据支付类型 {pay_type} 选择数据模板: {template_key}")
                return template_data.get(template_key, {})
            
            # 默认返回第一个模板
            first_value = next(iter(template_data.values()), {})
            return first_value
            
        except Exception as e:
            self.logger.error(f"获取默认数据模板失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return {}
    
    def set_base_url(self, base_url: str):
        """设置基础URL"""
        self.default_config['base_url'] = base_url
    
    def set_timeout(self, timeout: int):
        """设置请求超时时间"""
        self.default_config['timeout'] = timeout
    
    def set_verify_ssl(self, verify: bool):
        """设置SSL验证"""
        self.default_config['verify_ssl'] = verify
