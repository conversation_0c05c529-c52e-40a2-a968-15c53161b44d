- baseInfo:
    api_name: 开票接口
    url: /api/scene/invoice
    method: post
    header:
      Content-Type: application/x-www-form-urlencoded
    encrypt_type: TC_Base64
    data_default: deliver/invoice
    extract_default:
      serialNo_kp: $.invoiceData.serialNo
  testCase:
    - case_name: 企业开票
      data:
        invoiceTitle: "泰昌保险销售有限公司"
        invoiceType: 2
        taxpayerNo: "914503230617411288"
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respMsg: 请求成功
    - case_name: 个人开票
      data:
        invoiceTitle: "产品测试"
        invoiceType: 1
        taxpayerNo: ""
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respMsg: 请求成功