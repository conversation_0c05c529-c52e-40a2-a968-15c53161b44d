import allure
import pytest
import traceback
import time
import json
import copy
import atexit
import threading
import re
import os
import pickle
import tempfile
from pathlib import Path
from base.apiutil_business import RequestBase
from base.generateId import m_id, c_id
from common.recordlog import logs
from common.readyaml import ReadYamlData
from common.variable_processor import VariableProcessor
from base.context_storage import context_worker

class CrossProcessStepManager:
    """跨进程步骤状态管理器"""
    
    def __init__(self, workflow_name="default"):
        self.workflow_name = workflow_name
        # 使用项目根目录的绝对路径下的临时目录存储状态文件，确保所有进程使用同一路径
        import os
        project_root = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        self.temp_dir = Path(project_root) / "temp_contexts"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.status_file = self.temp_dir / "step_status.json"
        self.context_file = self.temp_dir / "step_contexts.json"
        self._lock_timeout = 10  # 锁超时时间
    
    def _safe_read_json(self, file_path):
        """安全读取JSON文件"""
        try:
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            pass
        return {}
    
    def _safe_write_json(self, file_path, data):
        """安全写入JSON文件"""
        try:
            # 写入临时文件，然后原子性移动
            temp_file = file_path.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 原子性移动文件
            if os.name == 'nt':  # Windows
                try:
                    if file_path.exists():
                        file_path.unlink()
                    temp_file.rename(file_path)
                except:
                    temp_file.replace(file_path)
            else:  # Unix-like
                temp_file.rename(file_path)
        except Exception as e:            logs.error(f"写入文件失败: {e}")
    
    def mark_step_completed(self, case_index, step_index, context_id):
        """标记步骤完成"""
        key = f"case:{case_index},step:{step_index}"
        
        # 确保目录存在
        logs.debug(f"检查目录: {self.temp_dir}, 存在: {self.temp_dir.exists()}")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        logs.debug(f"目录创建后: {self.temp_dir}, 存在: {self.temp_dir.exists()}")
        
        # 更新状态文件
        max_attempts = 10
        for attempt in range(max_attempts):
            try:
                status_data = self._safe_read_json(self.status_file)
                logs.debug(f"读取状态文件 {self.status_file}: {status_data}")
                status_data[key] = True
                self._safe_write_json(self.status_file, status_data)
                logs.debug(f"写入状态文件后: {status_data}")
                
                # 更新上下文文件
                context_data = self._safe_read_json(self.context_file)
                logs.debug(f"读取上下文文件 {self.context_file}: {context_data}")
                context_data[key] = context_id
                self._safe_write_json(self.context_file, context_data)
                logs.debug(f"写入上下文文件后: {context_data}")
                
                # 验证文件是否真的存在和内容是否正确
                if self.status_file.exists():
                    verify_status = self._safe_read_json(self.status_file)
                    logs.debug(f"验证状态文件存在并读取: {verify_status}")
                else:
                    logs.error(f"状态文件不存在: {self.status_file}")
                
                if self.context_file.exists():
                    verify_context = self._safe_read_json(self.context_file)
                    logs.debug(f"验证上下文文件存在并读取: {verify_context}")
                else:
                    logs.error(f"上下文文件不存在: {self.context_file}")
                
                logs.info(f"标记步骤完成: {key}, 上下文ID: {context_id}")
                break
            except Exception as e:
                logs.error(f"标记步骤完成异常 (尝试{attempt+1}/{max_attempts}): {e}")
                import traceback
                logs.error(f"异常堆栈: {traceback.format_exc()}")
                if attempt < max_attempts - 1:
                    time.sleep(0.1 * (attempt + 1))  # 指数退避
                    continue
                else:
                    logs.error(f"标记步骤完成失败: {e}")
    
    def wait_for_step_completion(self, forwardsign, max_wait_time=300):
        """等待步骤完成"""
        try:
            # 解析Forwardsign格式: case:0,step:2
            pattern = r'case:(\d+),step:(\d+)'
            match = re.match(pattern, forwardsign)
            if not match:
                logs.warning(f"Forwardsign格式无效: {forwardsign}, 应为 case:X,step:Y 格式")
                return None
            
            target_case_index = int(match.group(1))
            target_step_index = int(match.group(2))
            wait_key = f"case:{target_case_index},step:{target_step_index}"
            
            logs.info(f"等待步骤完成: {wait_key}")
            logs.debug(f"状态文件路径: {self.status_file.absolute()}")
            logs.debug(f"上下文文件路径: {self.context_file.absolute()}")
            logs.debug(f"状态文件存在: {self.status_file.exists()}")
            logs.debug(f"上下文文件存在: {self.context_file.exists()}")
            
            # 轮询等待目标步骤完成
            wait_start_time = time.time()
            check_count = 0
            while time.time() - wait_start_time < max_wait_time:
                try:
                    check_count += 1
                    status_data = self._safe_read_json(self.status_file)
                    
                    # 每10次检查打印一次调试信息
                    if check_count % 10 == 0:
                        logs.debug(f"等待检查 #{check_count}: 状态文件内容: {status_data}")
                        # 检查文件是否存在和大小
                        if self.status_file.exists():
                            logs.debug(f"状态文件存在，大小: {self.status_file.stat().st_size} bytes")
                        else:
                            logs.debug("状态文件不存在")
                    
                    if status_data.get(wait_key, False):
                        # 获取对应的上下文ID
                        context_data = self._safe_read_json(self.context_file)
                        target_context_id = context_data.get(wait_key)
                        logs.info(f"依赖步骤已完成: {wait_key}, 获取到上下文ID: {target_context_id}")
                        return target_context_id
                except Exception as e:
                    logs.warning(f"读取步骤状态失败: {e}")
                
                time.sleep(1)  # 每秒检查一次
            
            logs.error(f"等待步骤完成超时: {wait_key} (等待时间: {max_wait_time}秒)")
            return None
            
        except Exception as e:
            logs.error(f"等待步骤完成时发生错误: {e}")
            return None
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logs.info(f"清理跨进程状态文件: {self.temp_dir}")
        except Exception as e:
            logs.warning(f"清理跨进程状态文件失败: {e}")

@allure.epic('保险项目')
@allure.feature(next(m_id) + '投递服务-工作流测试')
@allure.parent_suite('保险项目')
@allure.suite('投递服务-工作流测试')
@allure.sub_suite('投保理赔完整流程')
class TestDeliverWorkflow:
    # 使用跨进程状态管理器
    _step_manager = None
    _setup_lock = threading.Lock()
    
    def setup_class(self):
        """类级别的setup，创建RequestBase实例"""
        self.context_worker = context_worker()
        # 注册清理函数        atexit.register(self.context_worker.cleanup_worker_context)
        
        # 初始化跨进程状态管理器
        with TestDeliverWorkflow._setup_lock:
            if TestDeliverWorkflow._step_manager is None:
                TestDeliverWorkflow._step_manager = CrossProcessStepManager("insure_claim_workflow")
    
    def teardown_class(self):
        """类级别的teardown，延迟清理worker上下文"""
        # 注意：不要在这里立即清理worker上下文，因为可能有其他worker依赖这些数据
        # 上下文清理将通过 atexit 在进程结束时自动进行
        logs.debug(f"测试类 {self.__class__.__name__} 完成，延迟清理上下文")
        
        # 清理跨进程状态（只在最后一个worker中清理）
        with TestDeliverWorkflow._setup_lock:
            if TestDeliverWorkflow._step_manager is not None:
                # 只在所有测试完成后清理，这里暂时不清理，让pytest session结束时清理
                pass

    @classmethod
    def _mark_step_completed(cls, case_index, step_index, context_id):
        """标记步骤完成并记录上下文ID"""
        if cls._step_manager:
            cls._step_manager.mark_step_completed(case_index, step_index, context_id)

    @classmethod
    def _wait_for_step_completion(cls, forwardsign, max_wait_time=300):
        """等待指定步骤完成并返回该步骤的上下文ID"""
        if cls._step_manager:
            return cls._step_manager.wait_for_step_completion(forwardsign, max_wait_time)
        return None

    def _should_skip_step_and_next(self, steps_data, current_step_index, case_index):
        """
        判断是否应该跳过当前步骤及后续通用步骤
        :param steps_data: 所有步骤数据
        :param current_step_index: 当前步骤索引
        :param case_index: 用例索引
        :return: (should_skip_current, should_skip_next_generic)
        """
        try:
            current_step = steps_data[current_step_index]
            _, _, current_testcase = current_step
            
            # 检查当前步骤是否需要跳过
            skip_current = current_testcase.get('skip_execution', False)
            
            if not skip_current:
                return False, False
            
            # 如果当前步骤需要跳过，检查下一个步骤是否为通用接口
            if current_step_index + 1 < len(steps_data):
                next_step = steps_data[current_step_index + 1]
                _, _, next_testcase = next_step
                
                # 判断下一个步骤是否为通用接口（没有具体数据或数据为通用数据）
                next_data = next_testcase.get('data', {})
                is_generic = (
                    not next_data or  # 没有数据
                    not isinstance(next_data, dict) or  # 数据不是字典
                    len(next_data) == 0 or  # 数据为空
                    all(key.startswith('{{') and key.endswith('}}') for key in next_data.keys())  # 全是变量引用
                )
                
                logs.info(f"下一步骤是否为通用接口: {is_generic}, 数据: {next_data}")
                return True, is_generic
            
            return True, False
            
        except Exception as e:
            logs.error(f"判断跳过逻辑时发生错误: {e}")
            return False, False

    @pytest.mark.run(order=1)
    @allure.story(next(c_id) + '投保理赔完整流程测试')
    @pytest.mark.smoke
    @pytest.mark.parametrize('steps_data',
                             ReadYamlData().get_workflow_yaml(file_path='testcase\DeliveryService\insureInvoce copy.yml'))
    def test_insurance_claim_workflow(self, steps_data):
        """
        测试投保到理赔的完整工作流程
        包含：投保 -> 查询投保状态 -> 申请理赔
        """
        if not steps_data:
            pytest.skip("工作流用例数据为空，跳过测试")
        # 从第一个步骤中获取工作流信息来设置标题和描述
        workflow_info = steps_data[0][0]
        allure.dynamic.title(workflow_info.get('workflow_name', '未命名工作流'))
        allure.attach(workflow_info.get('description', '无描述'), '工作流描述', allure.attachment_type.TEXT)

        logs.info(f"开始执行工作流: {workflow_info.get('workflow_name')}")

        request_base = RequestBase()
        # 创建工作流上下文ID
        context_id = self.context_worker.create_workflow_context()
        logs.info(f"创建工作流上下文ID: {context_id}")
        
        # 遍历并执行工作流中的每一步
        # 每个 step_data 是一个元组 (workflow_info, base_info, testcase)
        skip_next_generic = False  # 标记是否跳过下一个通用步骤
        
        for step_index, (workflow_info, base_info, testcase) in enumerate(steps_data):
            # 每个步骤都要重新获取case_index，因为不同步骤可能属于不同的case
            case_index = workflow_info.get('case_index', 0)
            original_workflow_name = workflow_info.get('original_workflow_name', workflow_info.get('workflow_name'))
            
            # 计算真实的步骤索引（在原始workflow中的位置）
            # 直接使用step_index，因为每个测试用例都是独立的
            real_step_index = step_index
            
            step_name = testcase.get('case_name', '未命名步骤')
            max_retry = testcase.pop('max_retry', 1)  # 获取并移除max_retry
            retry_interval = testcase.pop('retry_interval', 1)  # 获取并移除retry_interval
            skip_execution = testcase.pop('skip_execution', False)  # 获取并移除skip_execution
            forwardsign = testcase.pop('forwardsign', None)  # 获取并移除forwardsign
            logs.info("testcase: " + json.dumps(testcase, ensure_ascii=False, indent=2))
            # 检查是否需要跳过当前步骤
            if skip_execution:
                logs.info(f"跳过执行步骤: {step_name} (skip_execution=True)")
                continue

            # 处理前置依赖等待
            target_context_id = None  # 用于存储等待步骤的上下文ID
            if forwardsign:
                logs.info(f"步骤 {step_name} 检测到前置依赖: {forwardsign}")
                
                # 等待依赖步骤完成并获取其上下文ID
                target_context_id = self._wait_for_step_completion(forwardsign)
                if target_context_id is None:
                    logs.error(f"步骤 {step_name} 的前置依赖 {forwardsign} 等待超时或失败，跳过执行")
                    continue
                else:
                    logs.info(f"步骤 {step_name} 获取到依赖步骤的上下文ID: {target_context_id}")

            with allure.step(f"执行步骤: {step_name}"):
                allure.attach(f"步骤: {step_name}\n上下文ID: {context_id}\n当前用例索引：{case_index}\n真实步骤索引：{real_step_index}\n跳过执行: {skip_execution}\n前置依赖: {forwardsign}\n依赖上下文ID: {target_context_id}", 
                             '当前执行步骤', allure.attachment_type.TEXT)
                logs.info(f"执行步骤: {step_name}, case_index: {case_index}, step_index: {real_step_index}, 最大重试次数: {max_retry}")

                # 使用重试逻辑执行请求
                retry_count = 0
                last_exception = None
                success = False

                while retry_count < max_retry and not success:
                    try:
                        tc = copy.deepcopy(testcase)
                        case_name_from_data = None  # 初始化变量
                        try:
                            data_dict = tc.get('data', {})
                            if 'caseName' in data_dict:
                                case_name_from_data = data_dict.pop('caseName')
                                # base_info['case_name'] = case_name_from_data
                            if 'case_name' in tc and case_name_from_data:
                                tc['case_name'] = case_name_from_data
                        except (KeyError, AttributeError) as e:
                            logs.warning(f"处理caseName时出错: {e}")
                        if case_name_from_data:
                            logs.info(f"从数据中提取caseName: {case_name_from_data}")
                        if '申请理赔' in step_name:
                            try:
                                pay_type = tc.get('data', {}).pop('payType', 0)
                            except KeyError:
                                pay_type = 0
                            # 获取理赔对应数据
                            pay_default_data = request_base.get_template_data_by_pay_type(template_path='testcase/default_yaml/deliver/applyclaim.yml', pay_type=pay_type)
                            # 合并理赔数据
                            current_data = tc.get('data', {})
                            merged_data = VariableProcessor.merge_json_data(pay_default_data, current_data)
                            tc['data'] = merged_data                        # 对data进行处理
                        if 'data' in tc:
                            # 1. 进行上下文变量替换
                            tc_data = tc['data']
                            if tc_data:
                                # 如果有依赖步骤的上下文ID，优先使用依赖步骤的上下文进行变量替换
                                if target_context_id:
                                    logs.info(f"使用依赖步骤的上下文ID进行变量替换: {target_context_id}，fallback: {context_id}")
                                    # 使用优化后的方法，支持fallback机制
                                    tc['data'] = self.context_worker.replace_load_with_context(tc_data, target_context_id, context_id)
                                else:
                                    # 没有依赖，使用当前上下文进行变量替换
                                    tc['data'] = self.context_worker.replace_load_with_context_legacy(tc_data, context_id)
                                
                                # 使用VariableProcessor处理数据
                                tc['data'] = VariableProcessor.process_json_data(tc['data'])
                                # print(json.dumps(tc['data'], ensure_ascii=False, indent=2))
                                allure.attach(json.dumps(tc['data'], ensure_ascii=False, indent=2),
                                              '处理后的请求数据', allure.attachment_type.JSON)
                        if retry_count > 0:
                            logs.info(f"第 {retry_count + 1} 次重试步骤: {step_name}")
                            allure.attach(f"等待 {retry_interval} 秒后重试", '重试信息', allure.attachment_type.TEXT)
                            time.sleep(retry_interval)
                        # 执行请求
                        result = self.context_worker.specification_yaml_with_context(base_info, tc, context_id)                        # 如果是查询状态类接口，可能需要重试直到状态变更
                        if "查询" in step_name and result:
                            # 投保成功
                            if '投保成功' in result.text:
                                logs.info(f"步骤 {step_name} 执行成功，结果: {result}")
                            else:
                                # 如果是查询状态类接口，可能需要等待状态变更
                                logs.info(f"步骤 {step_name} 执行结果: {result.text}，等待状态变更")
                                time.sleep(retry_interval)
                                continue
                        
                        # 标记步骤完成，传递上下文ID，使用真实的步骤索引
                        self._mark_step_completed(case_index, real_step_index, context_id)
                        success = True

                    except Exception as e:
                        last_exception = e
                        retry_count += 1
                        if retry_count >= max_retry:
                            logs.error(f"步骤 {step_name} 执行失败，已达到最大重试次数 {max_retry}")
                            allure.attach(f"最终失败原因: {str(e)}", '错误信息', allure.attachment_type.TEXT)
                            allure.attach(traceback.format_exc(), '错误堆栈', allure.attachment_type.TEXT)
                            raise e
                        else:
                            logs.warning(f"步骤 {step_name} 第 {retry_count} 次执行失败: {str(e)}")
                    finally:
                        # 可选：手动清理当前测试的上下文
                        pass

        logs.info(f"工作流 {workflow_info.get('workflow_name')} 所有步骤执行成功")
