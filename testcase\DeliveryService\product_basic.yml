- baseInfo:
    api_name: 产品基础信息查询
    url: /api/product/basic
    method: post
    header:
      Content-Type: application/x-www-form-urlencoded
    encrypt_type: TC_Base64
    extract_default:
      fixPremium: $.result.priceDetailBaseVo[0].fixPremium
      sumAssured: $.result.priceDetailBaseVo[0].sumAssured
      planCode: $.result.planCode
  testCase:
    - case_name: 国内旅行险C
      data:
        planCode: "00200005703"
      validation:
        - eq:
            code: '0'

    - case_name: 国内旅行险D
      data:
        planCode: "00200005704"
      validation:
        - eq:
            code: '0'

    - case_name: 退改无忧险-1
      data:
        planCode: "00200005601"
      validation:
        - eq:
            code: '0'

    - case_name: 退改无忧险-2
      data:
        planCode: "00200005602"
      validation:
        - eq:
            code: '0'

    - case_name: 航班意外险-3
      data:
        planCode: "00200005303"
      validation:
        - eq:
            code: '0'

    - case_name: 航班意外险-4
      data:
        planCode: "00200005304"
      validation:
        - eq:
            code: '0'

    - case_name: 航班意外险-5
      data:
        planCode: "00200005305"
      validation:
        - eq:
            code: '0'

    - case_name: 航班意外险-6
      data:
        planCode: "00200005306"
      validation:
        - eq:
            code: '0'

    - case_name: 航班意外险-7
      data:
        planCode: "00200005307"
      validation:
        - eq:
            code: '0'

    - case_name: 行李损失险-4
      data:
        planCode: "00200005204"
      validation:
        - eq:
            code: '0'

    - case_name: 行李损失险-5
      data:
        planCode: "00200005205"
      validation:
        - eq:
            code: '0'

    - case_name: 航班延误险-2
      data:
        planCode: "00200005102"
      validation:
        - eq:
            code: '0'

    - case_name: 航班延误险-3
      data:
        planCode: "00200005103"
      validation:
        - eq:
            code: '0'

    - case_name: 航综1.0-2
      data:
        planCode: "00200005003"
      validation:
        - eq:
            code: '0'

    - case_name: 航综3.0-1
      data:
        planCode: "00200004901"
      validation:
        - eq:
            code: '0'

    - case_name: 航综3.0-2
      data:
        planCode: "00200004902"
      validation:
        - eq:
            code: '0'

    - case_name: 航综3.0-3
      data:
        planCode: "00200004903"
      validation:
        - eq:
            code: '0'

    - case_name: 航综3.0-4
      data:
        planCode: "00200004904"
      validation:
        - eq:
            code: '0'

    - case_name: 航综3.0-5
      data:
        planCode: "00200004905"
      validation:
        - eq:
            code: '0'

    - case_name: 航综3.0-6
      data:
        planCode: "00200004906"
      validation:
        - eq:
            code: '0'
