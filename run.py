import shutil
import pytest
import os
import webbrowser
import argparse
import sys
import subprocess
import multiprocessing
from conf.setting import REPORT_TYPE
from conf.env_manager import env_manager
from conf.updateXml import update_environment_xml
import os
import uuid
from pathlib import Path
import time
from common.recordlog import logs
from common.safe_auller import cleanup_old_reports,create_unique_report_dir,check_allure_installed,run_allure_serve



def confirm_environment(env_name):
    """确认是否使用指定环境"""
    if env_name == 'uat':
        return True
    
    print(f"您选择了 {env_name} 环境，请确认是否继续？")
    print(f"环境信息: {env_manager.environments[env_name]}")
    confirm = input("确认使用该环境吗？(y/n): ")
    return confirm.lower() in ('y', 'yes')

def get_worker_count(workers):
    """获取并行执行的worker数量"""
    if workers == 'auto':
        # 获取CPU核心数，但限制最大worker数以避免资源过度占用
        cpu_count = multiprocessing.cpu_count()
        return min(cpu_count, 6)  # 最多使用8个worker
    elif workers == 'logical':
        # 使用逻辑CPU数量
        return multiprocessing.cpu_count()
    else:
        try:
            return int(workers)
        except ValueError:
            print(f"警告: 无效的worker数量 '{workers}'，使用默认值2")
            return 2

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='运行测试用例')
    parser.add_argument('--env', '-e', default='uat', choices=env_manager.get_available_environments(),
                      help='指定测试环境，默认为uat')
    parser.add_argument('--workers', '-n', default='2', 
                      help='并行执行的worker数量。可选值: 数字(如2,4), auto(自动), logical(CPU核心数), 默认为1(串行)')
    parser.add_argument('--dist', default='worksteal',
                      choices=['loadfile', 'loadscope', 'worksteal', 'each'],
                      help='分发策略: loadfile(按文件), loadscope(按作用域), worksteal(工作窃取), each(每个worker运行所有测试)')
    args = parser.parse_args()
    
    # 设置环境
    if not env_manager.set_environment(args.env):
        print(f"错误：未知的环境 {args.env}")
        sys.exit(1)
    
    # 如果是prod或dev环境，需要确认
    if args.env in ('prod', 'dev') and not confirm_environment(args.env):
        print("已取消测试运行")
        sys.exit(0)
    
    print(f"使用环境: {args.env}")
    print(f"环境配置: {env_manager.get_current_env_config()}")
    
    # 获取worker数量
    worker_count = get_worker_count(args.workers)
    
    if worker_count > 1:
        print(f"并行执行模式: {worker_count} 个worker, 分发策略: {args.dist}")
    else:
        print("串行执行模式")

    try:
        if REPORT_TYPE == 'allure':
            # 检查allure是否已安装
            if not check_allure_installed():
                sys.exit(1)
            # 清理旧报告
            cleanup_old_reports()
            
            # 创建唯一的报告目录
            report_dir = create_unique_report_dir()
            logs.info(f"使用报告目录: {report_dir}")

            # 构建pytest参数
            pytest_args = [
                '-s', '-v', 
                f'--alluredir={report_dir}', 
                './services', 
                '--clean-alluredir',
                f'--junitxml={Path(report_dir).parent}/results.xml'
            ]
            # 如果worker数量大于1，添加并行参数
            if worker_count > 1:
                pytest_args.extend([
                    f'-n={worker_count}',
                    f'--dist={args.dist}'
                ])
            print(f"运行pytest参数: {pytest_args}")
            # 运行测试
            exit_code = pytest.main(pytest_args)

            # 更新并复制environment.xml文件
            update_environment_xml(args.env, env_manager.get_current_env_config(), report_dir)
            
            # 只有测试成功时才启动报告服务
            if exit_code == 0:
                if not run_allure_serve(report_dir, True):
                    sys.exit(1)
            else:
                logs.warning(f"测试执行返回码: {exit_code}，仍然生成报告")
                run_allure_serve(report_dir, True)

        elif REPORT_TYPE == 'tm':
            pytest_args = ['-vs', '--pytest-tmreport-name=testReport.html', '--pytest-tmreport-path=./report/tmreport']
            
            # 如果worker数量大于1，添加并行参数
            if worker_count > 1:
                pytest_args.extend([
                    f'-n={worker_count}',
                    f'--dist={args.dist}'
                ])
                
            pytest.main(pytest_args)
            webbrowser.open_new_tab(os.getcwd() + '/report/tmreport/testReport.html')
    except KeyboardInterrupt:
        print("\n测试执行被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n测试执行出错: {str(e)}")
        sys.exit(1)