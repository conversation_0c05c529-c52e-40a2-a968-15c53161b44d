import re
from typing import Dict, Any, Union
from .variable_generator import VariableGenerator
from .env_variables import env_variables

class VariableProcessor:
    @staticmethod
    def _is_variable(value: str) -> bool:
        """
        检查字符串是否是变量标记
        """
        return isinstance(value, str) and value.startswith('{{') and value.endswith('}}')

    @classmethod
    def _process_single_value(cls, value: str) -> str:
        """
        处理单个变量值
        """
        if not cls._is_variable(value):
            return value
            
        # 处理随机变量 {{$xxx}}
        if value.startswith('{{$'):
            return VariableGenerator.process_variable(value[2:-2])
            
        # 处理环境变量 {{xxx}}
        var_name = value[2:-2]
        # 首先从环境变量中获取
        env_value = env_variables.get(var_name)
        if env_value is not None:
            return str(env_value)
            
        # 如果环境变量中没有，则从 yaml 配置中获取
        try:
            from common.readyaml import ReadYamlData
            yaml_reader = ReadYamlData()
            yaml_value = yaml_reader.get_env_variable(var_name)
            if yaml_value is not None:
                return str(yaml_value)
        except ImportError:
            pass
            
        return value

    @classmethod
    def process_value(cls, value: Any) -> Any:
        """
        递归处理值中的变量
        """
        if isinstance(value, str):
            return cls._process_single_value(value)
        elif isinstance(value, dict):
            return {k: cls.process_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [cls.process_value(item) for item in value]
        return value

    @classmethod
    def merge_json_data(cls, base_json: Dict[str, Any], case_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并基础JSON数据和用例JSON数据
        :param base_json: 基础JSON数据
        :param case_json: 用例JSON数据
        :return: 合并后的JSON数据
        """
        if not base_json:
            return case_json or {}
        if not case_json:
            return base_json.copy()
            
        result = base_json.copy()
        
        def deep_update(original: Dict[str, Any], update: Dict[str, Any]):
            for key, value in update.items():
                if key in original:
                    if isinstance(original[key], dict) and isinstance(value, dict):
                        # 如果都是字典，递归合并
                        deep_update(original[key], value)
                    elif isinstance(original[key], list) and isinstance(value, list):
                        # 如果都是列表，合并列表元素
                        original[key] = cls._merge_lists(original[key], value)
                    else:
                        # 其他情况直接替换
                        original[key] = value
                else:
                    # 如果原始数据中没有这个key，直接添加
                    original[key] = value
        
        deep_update(result, case_json)
        return result
    
    @classmethod
    def _merge_lists(cls, base_list: list, case_list: list) -> list:
        """
        合并两个列表，优先保持基础列表的结构，只更新指定的字段
        :param base_list: 基础列表
        :param case_list: 用例列表
        :return: 合并后的列表
        """
        # 如果case_list为空，返回base_list
        if not case_list:
            return base_list.copy()
        
        # 如果base_list为空，返回case_list
        if not base_list:
            return case_list.copy()
        
        result = []
        
        # 遍历基础列表
        for i, base_item in enumerate(base_list):
            if i < len(case_list):
                case_item = case_list[i]
                
                if isinstance(base_item, dict) and isinstance(case_item, dict):
                    # 如果都是字典，递归合并
                    merged_item = cls.merge_json_data(base_item, case_item)
                    result.append(merged_item)
                else:
                    # 如果不是字典，用case_item替换base_item
                    result.append(case_item)
            else:
                # 如果case_list没有对应的元素，保留base_item
                result.append(base_item)
        
        # 如果case_list比base_list长，添加剩余的元素
        if len(case_list) > len(base_list):
            result.extend(case_list[len(base_list):])
        
        return result

    @classmethod
    def process_json_data(cls, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理JSON数据中的所有变量
        :param json_data: 原始JSON数据
        :return: 处理后的JSON数据
        """
        return cls.process_value(json_data) 