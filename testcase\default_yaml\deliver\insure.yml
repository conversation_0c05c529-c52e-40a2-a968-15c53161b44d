data_default:
  serialNo: "{{$string.uuid}}"
  partnerId: "{{partnerId}}"
  orderNo: "9Pzhuanyong9999"
  orderAmount: "1005.68"
  planCode: "{{planCode}}"
  applyType: "D"
  applyPeriod: "1"
  subOrderNo: "{{$string.alphanumeric(length=9)}}"
  effectTime: "{{$date.now|addMinutes(-1)}}"
  expiryTime: "{{$date.now|addDays(1)}}"
  requestTime: "{{$date.now}}"
  totalPremium: "{{fixPremium}}"
  sumAssured: "{{sumAssured}}"
  returnUrl: "{{returnUrl}}"
  memberId: "{{$string.nanoid}}"
  insuranceType: "1"
  separateAccount: "1"
  insuranceNo: ""
  rebookOrderNo: ""
  paySerialNo: ""
  payChannel: ""
  traceStatus: "1"
  traceCode: ""
  holderInfo:
    holderType: "1"
    holderName: "产品测试"
    holderGender: "F"
    holderBirthday: "1944-10-19"
    holderCardType: "1"
    identityStartDate: "2015-10-24"
    identityEndDate: "2065-10-25"
    holderCardNo: "530102194410193109"
    holderMobile: "***********"
    holderEmail: "<EMAIL>"
    holderIncome: "5.56"
    occupationCode: "1223"
    countryCode: "CN"
    provinceCode: "440000"
    cityCode: "440100"
    districtCode: "440106"
    streetCode: "*********"
    address: "天河区珠江新城华夏路10号"
    holderZip: "510623"
    incomeSource: "工资收入"
    marriage: "1"
    unitOfNature: "0"
  insuredInfo:
    insuredName: "产品测试"
    insuredGender: "F"
    insuredBirthday: "1944-10-19"
    insuredCardType: "1"
    insuredCardNo: "530102194410193109"
    identityStartDate: "2014-10-24"
    identityEndDate: "2065-10-24"
    insuredMobile: "18959291254"
    insuredEmail: "<EMAIL>"
    insuredRelation: "1"
    isSocialSecurity: "0"
    height: "175"
    weight: "120"
    marriage: "0"
    insuredIncome: "5.58"
    insuredNum: "1"
    occupationCode: "2335"
    countryCode: "CN"
    provinceCode: "440000"
    cityCode: "440100"
    districtCode: "*********"
    streetCode: "NYC001-A"
    address: "天河区珠江新城华夏路10号"
    insuredZip: "510623"
    insuredEname: "surename"
    benefitType: "1"
  extendInfo:
    ticketType: "1"
    ticketPrice: "100.25"
    ticketSuccTime: "{{$date.now|addHours(1)}}"
    trafficStartTime: "{{$date.now|addHours(1)}}"
    trafficEndTime: "{{$date.now|addHours(3)}}"
    ticketNo: "MU6213"
    trafficCompany: "CA"
    cityName: "浙江2"
    departure: "北京1"
    departureCode: "TGC"
    destination: "杭州2"
    destinationCode: "HGH"
    eTicketNo: "{{$string.alphanumeric(length=15)}}"
    ticketStatus: "1"
    trafficType: "空客A13"
    seatType: "1"
  saleInfo:
    salesSubject: "TC123456"
    salesCode: "COA2023001"
    salesType: "1"
    payAccount: "****************"
    bankName: "中国银行"
    ebsCollectionProject: "EBS2023001"
    payMentAccount: "****************"
    payMentBankName: "建设银行"
    ebsProject: "PAY2023001"
    travelCompany: "国旅"
    saleStatus: "单售"
    channelType: 1
    channelName: "测试"
    guestCustomerId: "CUST2023001" 