- baseInfo:
    api_name: 作废发票接口
    url: /api/scene/invalidatedInvoice
    method: post
    header:
      Content-Type: application/x-www-form-urlencoded
    encrypt_type: TC_Base64
    data_default: deliver/invalidatedInvoice
  testCase:
    - case_name: 作废发票接口
      data:
        serialNo: "{{serialNo_kp}}"
        tradeSerialNoList:
          - "{{tradeSerialNo}}"
      validation:
        - contains:
            status_code: 200
        - eq:
            $.respMsg: 请求成功