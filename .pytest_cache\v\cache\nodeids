["testcase/Delivery service/test_delicer_api.py::TestDeliveryService::test_insure[base_info0-testcase0]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_insurance[base_info0-testcase0]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_insurance[base_info1-testcase1]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_insurance[case_info0]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[NOTSET]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info0-testcase0]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info1-testcase1]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info10-testcase10]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info11-testcase11]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info12-testcase12]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info13-testcase13]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info14-testcase14]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info15-testcase15]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info16-testcase16]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info17-testcase17]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info18-testcase18]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info19-testcase19]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info2-testcase2]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info3-testcase3]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info4-testcase4]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info5-testcase5]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info6-testcase6]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info7-testcase7]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info8-testcase8]", "testcase/DeliveryService/test_deliver_server.py::TestDeliverServer::test_product_basic[base_info9-testcase9]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[NOTSET]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data0]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data10]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data11]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data12]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data13]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data14]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data15]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data16]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data17]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data18]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data19]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data1]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data20]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data21]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data22]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data23]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data24]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data25]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data26]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data27]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data28]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data29]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data2]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data30]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data31]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data32]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data33]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data34]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data35]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data36]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data37]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data38]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data39]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data3]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data40]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data41]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data42]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data43]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data44]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data45]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data46]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data47]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data48]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data49]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data4]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data50]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data51]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data52]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data53]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data54]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data55]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data56]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data57]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data58]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data59]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data5]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data60]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data61]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data6]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data7]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data8]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[steps_data9]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case0]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case1]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case2]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case3]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case4]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case5]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case6]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case7]", "testcase/DeliveryService/test_deliver_workflow.py::TestDeliverWorkflow::test_insurance_claim_workflow[workflow_case8]"]