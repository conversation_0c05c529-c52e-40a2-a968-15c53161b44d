import sys
# 或者对于Windows：
import msvcrt
import time
from conf import setting
import logging
import os
import time
from logging.handlers import RotatingFileHandler
import datetime

log_path = setting.FILE_PATH["LOG"]
if not os.path.exists(log_path): os.mkdir(log_path)
logfile_name = log_path + r"\test.{}.log".format(time.strftime("%Y%m%d"))

class SafeRotatingFileHandler(RotatingFileHandler):
    """线程安全的日志轮转处理器"""
    
    def doRollover(self):
        """安全的日志轮转"""
        try:
            # 尝试获取文件锁
            if self.stream:
                self.stream.close()
                self.stream = None
            
            # 重试机制
            for attempt in range(3):
                try:
                    super().doRollover()
                    break
                except (OSError, PermissionError) as e:
                    if attempt < 2:
                        time.sleep(0.1)  # 等待100ms后重试
                        continue
                    else:
                        # 如果轮转失败，继续使用当前文件
                        print(f"日志轮转失败，继续使用当前文件: {e}")
                        break
                        
        except Exception as e:
            print(f"日志轮转异常: {e}")
        finally:
            if not self.stream:
                self.stream = self._open()

class RecordLog:
    """日志模块"""

    def __init__(self):
        self.handle_overdue_log()

    def handle_overdue_log(self):
        """处理过期日志文件"""
        # 获取系统的当前时间
        now_time = datetime.datetime.now()
        # 日期偏移30天，最多保留30的日志文件，超过自动清理
        offset_date = datetime.timedelta(days=-30)
        # 获取前一天时间戳
        before_date = (now_time + offset_date).timestamp()
        # 找到目录下的文件
        files = os.listdir(log_path)
        for file in files:
            if os.path.splitext(file)[1]:
                filepath = log_path + "\\" + file
                file_create_time = os.path.getctime(filepath)  # 获取文件创建时间,返回时间戳
                # dateArray = datetime.datetime.fromtimestamp(file_createtime) #标准时间格式
                # print(dateArray.strftime("%Y--%m--%d %H:%M:%S"))
                if file_create_time < before_date:
                    os.remove(filepath)
                else:
                    continue

    def output_logging(self):
        """获取logger对象"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            logger.setLevel(setting.LOG_LEVEL)
            log_format = logging.Formatter(
                '%(levelname)s - %(asctime)s - %(filename)s:%(lineno)d -[%(module)s:%(funcName)s] - %(message)s')
            
            # 使用安全的轮转处理器
            fh = SafeRotatingFileHandler(filename=logfile_name, mode='a', maxBytes=5242880,
                                       backupCount=7, encoding='utf-8')
            fh.setLevel(setting.LOG_LEVEL)
            fh.setFormatter(log_format)
            logger.addHandler(fh)

            sh = logging.StreamHandler()
            sh.setLevel(setting.STREAM_LOG_LEVEL)
            sh.setFormatter(log_format)
            logger.addHandler(sh)
        return logger

apilog = RecordLog()
logs = apilog.output_logging()
