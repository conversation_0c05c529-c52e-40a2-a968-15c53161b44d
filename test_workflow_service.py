# -*- coding: utf-8 -*-
"""
独立测试脚本，验证保险理赔工作流服务是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入功能"""
    try:
        from services.insurance_workflow import (
            InsuranceClaimWorkflowService,
            create_workflow_service,
            execute_insurance_claim_workflow
        )
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_service_creation():
    """测试服务创建"""
    try:
        from services.insurance_workflow import create_workflow_service
        
        service = create_workflow_service()
        print("✅ 服务创建成功")
        
        # 测试上下文创建
        context_id = service.context_manager.create_workflow_context("测试工作流", 0)
        print(f"✅ 上下文创建成功: {context_id}")
        
        # 测试变量设置和获取
        service.set_context_variable(context_id, 'testKey', 'testValue')
        variables = service.get_context_variables(context_id)
        print(f"✅ 变量管理成功: {variables}")
        
        # 清理
        service.cleanup_context(context_id)
        print("✅ 上下文清理成功")
        
        return True
    except Exception as e:
        print(f"❌ 服务创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_variable_processor():
    """测试变量处理器"""
    try:
        from services.insurance_workflow.utils.variable_processor import WorkflowVariableProcessor
        
        processor = WorkflowVariableProcessor()
        
        # 测试变量处理
        test_data = {
            'uuid': '{{$string.uuid}}',
            'nanoid': '{{$string.nanoid}}',
            'date': '{{$date.now}}',
            'static': 'static_value'
        }
        
        processed_data = processor.process_json_data(test_data)
        print(f"✅ 变量处理成功: {processed_data}")
        
        return True
    except Exception as e:
        print(f"❌ 变量处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_step_config():
    """测试步骤配置"""
    try:
        from services.insurance_workflow.config.workflow_config import StepConfig
        
        step_config = StepConfig(
            step_name="测试步骤",
            api_config={
                'api_name': '测试接口',
                'url': '/test',
                'method': 'GET'
            },
            data={'test': 'value'}
        )
        
        print(f"✅ 步骤配置创建成功: {step_config.step_name}")
        return True
    except Exception as e:
        print(f"❌ 步骤配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_step_execution():
    """测试单步骤执行（模拟）"""
    try:
        from services.insurance_workflow import create_workflow_service
        
        service = create_workflow_service()
        
        # 创建一个简单的步骤配置（不实际发送请求）
        step_config = {
            'step_name': '模拟测试步骤',
            'api_config': {
                'api_name': '模拟接口',
                'url': 'http://httpbin.org/get',  # 使用公共测试API
                'method': 'GET'
            },
            'data': {},
            'max_retry': 1
        }
        
        print("✅ 步骤配置准备完成")
        
        # 注意：这里不实际执行，因为可能没有网络连接
        # step_result, context_id = service.execute_single_step(step_config)
        # print(f"✅ 单步骤执行成功: {step_result.success}")
        
        return True
    except Exception as e:
        print(f"❌ 单步骤执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试保险理赔工作流服务...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_import),
        ("服务创建测试", test_service_creation),
        ("变量处理器测试", test_variable_processor),
        ("步骤配置测试", test_step_config),
        ("单步骤执行测试", test_single_step_execution),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   测试失败")
        except Exception as e:
            print(f"   测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！保险理赔工作流服务运行正常。")
        print("\n📖 使用方法:")
        print("```python")
        print("from services.insurance_workflow import execute_insurance_claim_workflow")
        print("results = execute_insurance_claim_workflow('path/to/workflow.yml')")
        print("```")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
