# -*- coding: utf-8 -*-
"""
步骤执行器
封装单个步骤的执行逻辑，包括重试、变量替换、结果提取等功能
"""

import time
import json
import copy
import traceback
from typing import Dict, Any, Optional, Tuple
import allure
import jsonpath

from services.insurance_workflow.config.workflow_config import StepConfig
from services.insurance_workflow.core.context_manager import WorkflowContextManager
from services.insurance_workflow.utils.request_handler import WorkflowRequestHandler
from services.insurance_workflow.utils.variable_processor import WorkflowVariableProcessor
from services.insurance_workflow.utils.logger import get_workflow_logger


class StepExecutionResult:
    """步骤执行结果"""
    
    def __init__(self, success: bool, response: Any = None, error: str = "", 
                 extracted_data: Dict[str, Any] = None, retry_count: int = 0):
        self.success = success
        self.response = response
        self.error = error
        self.extracted_data = extracted_data or {}
        self.retry_count = retry_count
        self.execution_time = time.time()


class StepExecutor:
    """步骤执行器"""
    
    def __init__(self, context_manager: WorkflowContextManager, 
                 request_handler: WorkflowRequestHandler,
                 variable_processor: WorkflowVariableProcessor):
        """
        初始化步骤执行器
        :param context_manager: 上下文管理器
        :param request_handler: 请求处理器
        :param variable_processor: 变量处理器
        """
        self.context_manager = context_manager
        self.request_handler = request_handler
        self.variable_processor = variable_processor
        self.logger = get_workflow_logger()
    
    def execute_step(self, step_config: StepConfig, context_id: str, 
                     target_context_id: Optional[str] = None) -> StepExecutionResult:
        """
        执行单个步骤
        :param step_config: 步骤配置
        :param context_id: 当前上下文ID
        :param target_context_id: 依赖的目标上下文ID
        :return: 步骤执行结果
        """
        step_name = step_config.step_name
        
        # 检查是否需要跳过执行
        if step_config.skip_execution:
            self.logger.info(f"跳过执行步骤: {step_name} (skip_execution=True)")
            return StepExecutionResult(success=True, error="步骤被跳过")
        
        self.logger.info(f"开始执行步骤: {step_name}")
        
        with allure.step(f"执行步骤: {step_name}"):
            # 添加Allure附件
            allure.attach(
                f"步骤: {step_name}\n"
                f"上下文ID: {context_id}\n"
                f"依赖上下文ID: {target_context_id}\n"
                f"跳过执行: {step_config.skip_execution}\n"
                f"前置依赖: {step_config.forwardsign}",
                '当前执行步骤信息',
                allure.attachment_type.TEXT
            )
            
            # 执行重试逻辑
            return self._execute_with_retry(step_config, context_id, target_context_id)
    
    def _execute_with_retry(self, step_config: StepConfig, context_id: str, 
                           target_context_id: Optional[str] = None) -> StepExecutionResult:
        """
        带重试的步骤执行
        :param step_config: 步骤配置
        :param context_id: 当前上下文ID
        :param target_context_id: 依赖的目标上下文ID
        :return: 步骤执行结果
        """
        step_name = step_config.step_name
        max_retry = step_config.max_retry
        retry_interval = step_config.retry_interval
        
        retry_count = 0
        last_exception = None
        
        while retry_count < max_retry:
            try:
                # 准备测试用例数据
                testcase = self._prepare_testcase_data(step_config, context_id, target_context_id)
                # 进行变量替换
                
                if retry_count > 0:
                    self.logger.info(f"第 {retry_count + 1} 次重试步骤: {step_name}")
                    allure.attach(f"等待 {retry_interval} 秒后重试", '重试信息', allure.attachment_type.TEXT)
                    time.sleep(retry_interval)
                
                # 执行API请求
                response = self.request_handler.execute_request(
                    step_config.api_config, testcase, context_id
                )
                
                # 处理查询状态类接口的特殊逻辑
                if self._is_query_step(step_name) and response:
                    if not self._check_query_success(response, step_name):
                        self.logger.info(f"步骤 {step_name} 查询状态未达到预期，等待状态变更")
                        time.sleep(retry_interval)
                        retry_count += 1
                        continue
                
                # 提取变量
                extracted_data = self._extract_variables(step_config, response, context_id)
                
                # 记录执行结果
                result = StepExecutionResult(
                    success=True,
                    response=response,
                    extracted_data=extracted_data,
                    retry_count=retry_count
                )
                
                self.logger.info(f"步骤 {step_name} 执行成功")
                return result
                
            except Exception as e:
                last_exception = e
                retry_count += 1
                
                if retry_count >= max_retry:
                    error_msg = f"步骤 {step_name} 执行失败，已达到最大重试次数 {max_retry}"
                    self.logger.error(error_msg)
                    allure.attach(f"最终失败原因: {str(e)}", '错误信息', allure.attachment_type.TEXT)
                    allure.attach(traceback.format_exc(), '错误堆栈', allure.attachment_type.TEXT)
                    
                    return StepExecutionResult(
                        success=False,
                        error=str(e),
                        retry_count=retry_count
                    )
                else:
                    self.logger.warning(f"步骤 {step_name} 第 {retry_count} 次执行失败: {str(e)}")
        
        # 不应该到达这里
        return StepExecutionResult(
            success=False,
            error="未知错误",
            retry_count=retry_count
        )
    
    def _prepare_testcase_data(self, step_config: StepConfig, context_id: str, 
                              target_context_id: Optional[str] = None) -> Dict[str, Any]:
        """
        准备测试用例数据
        :param step_config: 步骤配置
        :param context_id: 当前上下文ID
        :param target_context_id: 依赖的目标上下文ID
        :return: 处理后的测试用例数据
        """
        # 深拷贝避免修改原始数据
        testcase = copy.deepcopy({
            'case_name': step_config.step_name,
            'data': step_config.data,
            'validation': step_config.validation,
            'extract': step_config.extract
        })
        
        # 处理理赔步骤的特殊逻辑
        if '申请理赔' in step_config.step_name:
            testcase = self._handle_claim_step_data(testcase, step_config)
        
        # 进行变量替换
        if 'data' in testcase and testcase['data']:
            # 使用上下文变量替换
            if target_context_id:
                self.logger.info(f"使用依赖步骤的上下文ID进行变量替换: {target_context_id}，fallback: {context_id}")
                testcase['data'] = self.context_manager.replace_variables_in_data(
                    target_context_id, testcase['data'], context_id
                )
            else:
                testcase['data'] = self.context_manager.replace_variables_in_data(
                    context_id, testcase['data']
                )
            
            # 使用变量处理器进行进一步处理
            testcase['data'] = self.variable_processor.process_json_data(testcase['data'])
            
            # 添加Allure附件
            allure.attach(
                json.dumps(testcase['data'], ensure_ascii=False, indent=2),
                '处理后的请求数据',
                allure.attachment_type.JSON
            )
        
        return testcase
    
    def _handle_claim_step_data(self, testcase: Dict[str, Any], step_config: StepConfig) -> Dict[str, Any]:
        """
        处理理赔步骤的特殊数据逻辑
        :param testcase: 测试用例数据
        :param step_config: 步骤配置
        :return: 处理后的测试用例数据
        """
        try:
            # 获取支付类型
            pay_type = testcase.get('data', {}).pop('payType', 0)
            
            # 获取理赔对应的默认数据
            claim_default_data = self.request_handler.get_template_data_by_pay_type(
                template_path='testcase/default_yaml/deliver/applyclaim.yml',
                pay_type=pay_type
            )
            
            # 合并理赔数据
            current_data = testcase.get('data', {})
            merged_data = self.variable_processor.merge_json_data(claim_default_data, current_data)
            testcase['data'] = merged_data
            
        except Exception as e:
            self.logger.warning(f"处理理赔步骤数据时出错: {e}")
        
        return testcase
    
    def _extract_variables(self, step_config: StepConfig, response: Any, context_id: str) -> Dict[str, Any]:
        """
        从响应中提取变量
        :param step_config: 步骤配置
        :param response: API响应
        :param context_id: 上下文ID
        :return: 提取的变量字典
        """
        extracted_data = {}
        
        if not step_config.extract or not response:
            return extracted_data
        
        try:
            response_text = response.text
            self.logger.info(f"开始提取上下文变量: {step_config.extract}")
            
            for key, value in step_config.extract.items():
                if "$" in value:
                    try:
                        response_json = json.loads(response_text)
                        ext_json = jsonpath.jsonpath(response_json, value)
                        if ext_json:
                            extracted_data[key] = ext_json[0]
                            self.logger.info(f"成功提取变量: {key} = {ext_json[0]}")
                            allure.attach(
                                str(ext_json[0]),
                                f'{key}提取到的参数:{ext_json[0]}',
                                allure.attachment_type.TEXT
                            )
                        else:
                            extracted_data[key] = "未提取到数据，该接口返回结果可能为空"
                            self.logger.warning(f"未能提取变量: {key}, 表达式: {value}")
                    except json.JSONDecodeError as jde:
                        self.logger.error(f"JSON解析失败: {jde}")
                    except Exception as ee:
                        self.logger.error(f"提取变量 {key} 时发生错误: {ee}")
            
            # 将提取的变量存储到上下文中
            if extracted_data:
                self.context_manager.set_variables(context_id, extracted_data)
                self.logger.info(f'上下文{context_id}提取到参数：{extracted_data}')
                
        except Exception as e:
            error_msg = f'上下文变量提取异常：{e}'
            self.logger.error(error_msg)
            allure.attach(error_msg, '变量提取错误', allure.attachment_type.TEXT)
        
        return extracted_data
    
    def _is_query_step(self, step_name: str) -> bool:
        """判断是否为查询步骤"""
        return "查询" in step_name
    
    def _check_query_success(self, response: Any, step_name: str) -> bool:
        """检查查询步骤是否成功"""
        try:
            if hasattr(response, 'text') and '投保成功' in response.text:
                self.logger.info(f"步骤 {step_name} 执行成功，结果: {response}")
                return True
            else:
                return False
        except Exception:
            return False
